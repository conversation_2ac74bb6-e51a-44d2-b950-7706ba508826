# This python code measures semantic similarity between the input provided to the LLM and its response content, generated by SCassist
# bert-similarity.py
# Author: <PERSON> PhD
# Code assistant: Gemini

#source /data/conda.sh
conda update -n base -c conda-forge conda
conda create -n mybert python=3.9.15 transformers
conda activate mybert

#mkdir -pv /data/bert

from transformers import BertTokenizer, BertModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
import random

# Set the random seed
torch.manual_seed(42)
random.seed(42)

# Load the BERT tokenizer and model
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
model = BertModel.from_pretrained('bert-base-uncased')

# Example sentences
# sentence1 = "IAN in an intelligent system for omics discovery"
# sentence2 = "SCassist in an intelligent assistant for single cell omics analysis "

# Read content from text files
with open("sentence1.txt", "r") as f:
    sentence1 = f.read().strip()

with open("sentence2.txt", "r") as f:
    sentence2 = f.read().strip()

# Tokenize the sentences
tokens1 = tokenizer.tokenize(sentence1)
tokens2 = tokenizer.tokenize(sentence2)

# Add [CLS] and [SEP] tokens
tokens1 = ['[CLS]'] + tokens1 + ['[SEP]']
tokens2 = ['[CLS]'] + tokens2 + ['[SEP]']

# Segment the tokens into chunks
max_length = 512
chunk_size = max_length - 2  # Account for [CLS] and [SEP]
chunks1 = [tokens1[i:i + chunk_size] for i in range(0, len(tokens1), chunk_size)]
chunks2 = [tokens2[i:i + chunk_size] for i in range(0, len(tokens2), chunk_size)]

# Process each chunk
embeddings1 = []
embeddings2 = []
for chunk1, chunk2 in zip(chunks1, chunks2):
    # Add [CLS] and [SEP] tokens
    chunk1 = ['[CLS]'] + chunk1 + ['[SEP]']
    chunk2 = ['[CLS]'] + chunk2 + ['[SEP]']

# Convert tokens to input IDs
input_ids1 = torch.tensor(tokenizer.convert_tokens_to_ids(chunk1)).unsqueeze(0)
input_ids2 = torch.tensor(tokenizer.convert_tokens_to_ids(chunk2)).unsqueeze(0)

# Obtain the BERT embeddings
with torch.no_grad():
    outputs1 = model(input_ids1)
    outputs2 = model(input_ids2)
    embeddings1.append(outputs1.last_hidden_state[:, 0, :])
    embeddings2.append(outputs2.last_hidden_state[:, 0, :])

# Concatenate the embeddings from all chunks
embeddings1 = torch.cat(embeddings1, dim=0)
embeddings2 = torch.cat(embeddings2, dim=0)

# Calculate similarity
similarity_score = cosine_similarity(embeddings1, embeddings2)
print("Similarity Score:", similarity_score)

# Reference:
# https://www.geeksforgeeks.org/sentence-similarity-using-bert-transformer/
