% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_multi_agent_system.R
\name{Agent}
\alias{Agent}
\title{Agent Class}
\description{
Agent Class

Agent Class
}
\details{
Represents an agent in the multi-agent system.
}
\section{Methods}{
\subsection{Public methods}{
\itemize{
\item \href{#method-Agent-new}{\code{Agent$new()}}
\item \href{#method-Agent-get_response}{\code{Agent$get_response()}}
\item \href{#method-Agent-clone}{\code{Agent$clone()}}
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Agent-new"></a>}}
\if{latex}{\out{\hypertarget{method-Agent-new}{}}}
\subsection{Method \code{new()}}{
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Agent$new(id, prompt, prompt_type)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{id}}{The ID of the agent.}

\item{\code{prompt}}{The prompt for the agent.}

\item{\code{prompt_type}}{The type of prompt for the agent.}
}
\if{html}{\out{</div>}}
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Agent-get_response"></a>}}
\if{latex}{\out{\hypertarget{method-Agent-get_response}{}}}
\subsection{Method \code{get_response()}}{
Gets the response from the Gemini API.
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Agent$get_response(
  make_gemini_request_func,
  temperature,
  max_output_tokens,
  api_key,
  model_query,
  delay_seconds
)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{make_gemini_request_func}}{The function to make the Gemini API request.}

\item{\code{temperature}}{Numeric value controlling the randomness of the response.}

\item{\code{max_output_tokens}}{Integer specifying the maximum number of tokens in the response.}

\item{\code{api_key}}{Character string containing the Gemini API key.}

\item{\code{model_query}}{Character string specifying the model to query.}

\item{\code{delay_seconds}}{Numeric value specifying the delay in seconds after sending the request.}
}
\if{html}{\out{</div>}}
}
\subsection{Returns}{
The response from the Gemini API.
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Agent-clone"></a>}}
\if{latex}{\out{\hypertarget{method-Agent-clone}{}}}
\subsection{Method \code{clone()}}{
The objects of this class are cloneable with this method.
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Agent$clone(deep = FALSE)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{deep}}{Whether to make a deep clone.}
}
\if{html}{\out{</div>}}
}
}
}
