# 8.3 PyTorchVideo简介

![](./figures/logo2.jpg)

近几年来，随着传播媒介和视频平台的发展，视频正在取代图片成为下一代的主流媒体，这也使得有关视频的深度学习模型正在获得越来越多的关注。然而，有关视频的深度学习模型仍然有着许多缺点：

- 计算资源耗费更多，并且没有高质量的`model zoo`，不能像图片一样进行迁移学习和论文复现。
- 数据集处理较麻烦，但没有一个很好的视频处理工具。
- 随着多模态越来越流行，亟需一个工具来处理其他模态。

除此之外，还有部署优化等问题，为了解决这些问题，Meta推出了`PyTorchVideo`深度学习库（包含组件如Figure 1所示）。PyTorchVideo 是一个专注于视频理解工作的深度学习库。PytorchVideo 提供了加速视频理解研究所需的可重用、模块化和高效的组件。PyTorchVideo 是使用[PyTorch](https://pytorch.org/)开发的，支持不同的深度学习视频组件，如视频模型、视频数据集和视频特定转换。

![](./figures/list.png)



## 8.3.1 PyTorchVideo的主要部件和亮点

PytorchVideo 提供了加速视频理解研究所需的模块化和高效的API。它还支持不同的深度学习视频组件，如视频模型、视频数据集和视频特定转换，最重要的是，PytorchVideo也提供了model zoo，使得人们可以使用各种先进的预训练视频模型及其评判基准。PyTorchVideo主要亮点如下：

- **基于 PyTorch：**使用 PyTorch 构建。使所有 PyTorch 生态系统组件的使用变得容易。
- **Model Zoo：**PyTorchVideo提供了包含I3D、R(2+1)D、SlowFast、X3D、MViT等SOTA模型的高质量model zoo（目前还在快速扩充中，未来会有更多SOTA model），并且PyTorchVideo的model zoo调用与[PyTorch Hub](https://link.zhihu.com/?target=https%3A//pytorch.org/hub/)做了整合，大大简化模型调用，具体的一些调用方法可以参考下面的【使用 PyTorchVideo model zoo】部分。

- **数据预处理和常见数据**，PyTorchVideo支持Kinetics-400, Something-Something V2, Charades, Ava (v2.2), Epic Kitchen, HMDB51, UCF101, Domsev等主流数据集和相应的数据预处理，同时还支持randaug, augmix等数据增强trick。
- **模块化设计**：PyTorchVideo的设计类似于torchvision，也是提供许多模块方便用户调用修改，在PyTorchVideo中具体来说包括data, transforms, layer, model, accelerator等模块，方便用户进行调用和读取。
- **支持多模态**：PyTorchVideo现在对多模态的支持包括了visual和audio，未来会支持更多模态，为多模态模型的发展提供支持。
- **移动端部署优化**：PyTorchVideo支持针对移动端模型的部署优化（使用前述的PyTorchVideo/accelerator模块），模型经过PyTorchVideo优化了最高达**7倍**的提速，并实现了第一个能实时跑在手机端的X3D模型（实验中可以实时跑在2018年的三星Galaxy S8上，具体请见[Android Demo APP](https://github.com/pytorch/android-demo-app/tree/master/TorchVideo)）。



## 8.3.2 PyTorchVideo的安装

我们可以直接使用pip来安装PyTorchVideo：

```shell
pip install pytorchvideo
```

注：

- 安装的虚拟环境的python版本 >= 3.7
- PyTorch >= 1.8.0，安装的torchvision也需要匹配
- CUDA >= 10.2
- ioPath：[具体情况](https://github.com/facebookresearch/iopath)
- fvcore版本 >= 0.1.4：[具体情况](https://github.com/facebookresearch/fvcore)



## 8.3.3 Model zoo 和 benchmark

在下面这部分，我将简单介绍些PyTorchVideo所提供的Model zoo和benchmark

- Kinetics-400

| arch     | depth | pretrain | frame length x sample rate | top 1 | top 5 | Flops (G) x views | Params (M) | Model                                                        |
| -------- | ----- | -------- | -------------------------- | ----- | ----- | ----------------- | ---------- | ------------------------------------------------------------ |
| C2D      | R50   | \-       | 8x8                        | 71.46 | 89.68 | 25.89 x 3 x 10    | 24.33      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/C2D\_8x8\_R50.pyth) |
| I3D      | R50   | \-       | 8x8                        | 73.27 | 90.70 | 37.53 x 3 x 10    | 28.04      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/I3D\_8x8\_R50.pyth) |
| Slow     | R50   | \-       | 4x16                       | 72.40 | 90.18 | 27.55 x 3 x 10    | 32.45      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOW\_4x16\_R50.pyth) |
| Slow     | R50   | \-       | 8x8                        | 74.58 | 91.63 | 54.52 x 3 x 10    | 32.45      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOW\_8x8\_R50.pyth) |
| SlowFast | R50   | \-       | 4x16                       | 75.34 | 91.89 | 36.69 x 3 x 10    | 34.48      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST\_4x16\_R50.pyth) |
| SlowFast | R50   | \-       | 8x8                        | 76.94 | 92.69 | 65.71 x 3 x 10    | 34.57      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST\_8x8\_R50.pyth) |
| SlowFast | R101  | \-       | 8x8                        | 77.90 | 93.27 | 127.20 x 3 x 10   | 62.83      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST\_8x8\_R101.pyth) |
| SlowFast | R101  | \-       | 16x8                       | 78.70 | 93.61 | 215.61 x 3 x 10   | 53.77      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST\_16x8\_R101_50_50.pyth) |
| CSN      | R101  | \-       | 32x2                       | 77.00 | 92.90 | 75.62 x 3 x 10    | 22.21      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/CSN\_32x2\_R101.pyth) |
| R(2+1)D  | R50   | \-       | 16x4                       | 76.01 | 92.23 | 76.45 x 3 x 10    | 28.11      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/R2PLUS1D\_16x4\_R50.pyth) |
| X3D      | XS    | \-       | 4x12                       | 69.12 | 88.63 | 0.91 x 3 x 10     | 3.79       | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D\_XS.pyth) |
| X3D      | S     | \-       | 13x6                       | 73.33 | 91.27 | 2.96 x 3 x 10     | 3.79       | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D\_S.pyth) |
| X3D      | M     | \-       | 16x5                       | 75.94 | 92.72 | 6.72 x 3 x 10     | 3.79       | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D\_M.pyth) |
| X3D      | L     | \-       | 16x5                       | 77.44 | 93.31 | 26.64 x 3 x 10    | 6.15       | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D\_L.pyth) |
| MViT     | B     | \-       | 16x4                       | 78.85 | 93.85 | 70.80 x 1 x 5     | 36.61      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/MVIT\_B\_16x4.pyth) |
| MViT     | B     | \-       | 32x3                       | 80.30 | 94.69 | 170.37 x 1 x 5    | 36.61      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/MVIT\_B\_32x3\_f294077834.pyth) |

- Something-Something V2

| arch     | depth | pretrain     | frame length x sample rate | top 1 | top 5 | Flops (G) x views | Params (M) | Model                                                        |
| -------- | ----- | ------------ | -------------------------- | ----- | ----- | ----------------- | ---------- | ------------------------------------------------------------ |
| Slow     | R50   | Kinetics 400 | 8x8                        | 60.04 | 85.19 | 55.10 x 3 x 1     | 31.96      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ssv2/SLOW\_8x8\_R50.pyth) |
| SlowFast | R50   | Kinetics 400 | 8x8                        | 61.68 | 86.92 | 66.60 x 3 x 1     | 34.04      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ssv2/SLOWFAST\_8x8\_R50.pyth) |

- Charades

| arch     | depth | pretrain     | frame length x sample rate | MAP   | Flops (G) x views | Params (M) | Model                                                        |
| -------- | ----- | ------------ | -------------------------- | ----- | ----------------- | ---------- | ------------------------------------------------------------ |
| Slow     | R50   | Kinetics 400 | 8x8                        | 34.72 | 55.10 x 3 x 10    | 31.96      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/charades/SLOW\_8x8\_R50.pyth) |
| SlowFast | R50   | Kinetics 400 | 8x8                        | 37.24 | 66.60 x 3 x 10    | 34.00      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/charades/SLOWFAST\_8x8\_R50.pyth) |

- AVA (V2.2)

| arch     | depth | pretrain     | frame length x sample rate | MAP   | Params (M) | Model                                                        |
| -------- | ----- | ------------ | -------------------------- | ----- | ---------- | ------------------------------------------------------------ |
| Slow     | R50   | Kinetics 400 | 4x16                       | 19.5  | 31.78      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ava/SLOW\_4x16\_R50\_DETECTION.pyth) |
| SlowFast | R50   | Kinetics 400 | 8x8                        | 24.67 | 33.82      | [link](https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ava/SLOWFAST\_8x8\_R50\_DETECTION.pyth) |



## 8.3.4 使用 PyTorchVideo model zoo

PyTorchVideo提供了三种使用方法，并且给每一种都配备了`tutorial`

* TorchHub，这些模型都已经在TorchHub存在。我们可以根据实际情况来选择需不需要使用预训练模型。除此之外，官方也给出了TorchHub使用的 [tutorial](https://pytorchvideo.org/docs/tutorial_torchhub_inference) 。
* PySlowFast，使用 [PySlowFast workflow](https://github.com/facebookresearch/SlowFast/) 去训练或测试PyTorchVideo models/datasets.
* [PyTorch Lightning](https://github.com/PyTorchLightning/pytorch-lightning)建立一个工作流进行处理，点击查看官方 [tutorial](https://pytorchvideo.org/docs/tutorial_classification)。

- 如果想查看更多的使用教程，可以点击 [这里](https://github.com/facebookresearch/pytorchvideo/tree/main/tutorials) 进行尝试

总的来说，PyTorchVideo的使用与torchvision的使用方法类似，在有了前面的学习基础上，我们可以很快上手PyTorchVideo，具体的我们可以通过查看官方提供的文档和一些例程来了解使用方法：[官方网址](https://pytorchvideo.readthedocs.io/en/latest/index.html)

