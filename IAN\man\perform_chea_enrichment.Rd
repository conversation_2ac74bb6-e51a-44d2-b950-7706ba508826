% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_enrichment_analysis.R
\name{perform_chea_enrichment}
\alias{perform_chea_enrichment}
\title{Perform ChEA Enrichment Analysis}
\usage{
perform_chea_enrichment(
  gene_symbols,
  organism,
  pvalue = 0.05,
  output_dir = "enrichment_results"
)
}
\arguments{
\item{gene_symbols}{A vector of gene symbols.}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse".}

\item{pvalue}{Numeric value specifying the p-value threshold for filtering results. Default is 0.05.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A data frame containing the filtered ChEA enrichment results, or a list with an "error" element if the analysis fails.
}
\description{
Performs ChEA (ChIP-X Enrichment Analysis) using the `enrichr` function from the `enrichR` package.
}
