% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_llm_prompts.R
\name{create_llm_prompt_go}
\alias{create_llm_prompt_go}
\title{Create LLM Prompt for GO Enrichment Analysis}
\usage{
create_llm_prompt_go(
  enrichment_results,
  analysis_type,
  chea_results = NULL,
  string_results = NULL,
  gene_symbols = NULL,
  string_network_properties = NULL,
  experimental_design = NULL
)
}
\arguments{
\item{enrichment_results}{A data frame containing GO enrichment results. Must have columns "Description", "pvalue", and "Gene".}

\item{analysis_type}{Character string specifying the type of analysis (e.g., "GO").}

\item{chea_results}{A data frame containing ChEA transcription factor enrichment results (optional).}

\item{string_results}{A list containing STRING protein-protein interaction data (optional).}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{string_network_properties}{A data frame containing STRING network properties (optional).}

\item{experimental_design}{A character string describing the experimental design (optional).}
}
\value{
A character string containing the LLM prompt.
}
\description{
Creates a prompt for a Large Language Model (LLM) to analyze Gene Ontology (GO) enrichment results,
along with other relevant data such as ChEA transcription factor enrichment and STRING protein-protein interaction data.
}
