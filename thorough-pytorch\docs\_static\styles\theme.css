/* Provided by <PERSON><PERSON><PERSON>'s 'basic' theme, and included in the final set of assets */
@import "../basic.css";

:root {
  /*****************************************************************************
  * Theme config
  **/
  --pst-header-height: 60px;

  /*****************************************************************************
  * Font size
  **/
  --pst-font-size-base: 15px; /* base font size - applied at body / html level */

  /* heading font sizes */
  --pst-font-size-h1: 36px;
  --pst-font-size-h2: 32px;
  --pst-font-size-h3: 26px;
  --pst-font-size-h4: 21px;
  --pst-font-size-h5: 18px;
  --pst-font-size-h6: 16px;

  /* smaller then heading font sizes*/
  --pst-font-size-milli: 12px;

  --pst-sidebar-font-size: 0.9em;
  --pst-sidebar-caption-font-size: 0.9em;

  /*****************************************************************************
  * Font family
  **/
  /* These are adapted from https://systemfontstack.com/ */
  --pst-font-family-base-system: -apple-system, BlinkMacSystemFont, Segoe UI,
    "Helvetica Neue", Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji,
    Segoe UI Symbol;
  --pst-font-family-monospace-system: "SFMono-Regular", Menlo, Consolas, Monaco,
    Liberation Mono, Lucida Console, monospace;

  --pst-font-family-base: var(--pst-font-family-base-system);
  --pst-font-family-heading: var(--pst-font-family-base);
  --pst-font-family-monospace: var(--pst-font-family-monospace-system);

  /*****************************************************************************
  * Color
  *
  * Colors are defined in rgb string way, "red, green, blue"
  **/
  --pst-color-primary: 19, 6, 84;
  --pst-color-success: 40, 167, 69;
  --pst-color-info: 0, 123, 255; /*23, 162, 184;*/
  --pst-color-warning: 255, 193, 7;
  --pst-color-danger: 220, 53, 69;
  --pst-color-text-base: 51, 51, 51;

  --pst-color-h1: var(--pst-color-primary);
  --pst-color-h2: var(--pst-color-primary);
  --pst-color-h3: var(--pst-color-text-base);
  --pst-color-h4: var(--pst-color-text-base);
  --pst-color-h5: var(--pst-color-text-base);
  --pst-color-h6: var(--pst-color-text-base);
  --pst-color-paragraph: var(--pst-color-text-base);
  --pst-color-link: 0, 91, 129;
  --pst-color-link-hover: 227, 46, 0;
  --pst-color-headerlink: 198, 15, 15;
  --pst-color-headerlink-hover: 255, 255, 255;
  --pst-color-preformatted-text: 34, 34, 34;
  --pst-color-preformatted-background: 250, 250, 250;
  --pst-color-inline-code: 232, 62, 140;

  --pst-color-active-navigation: 19, 6, 84;
  --pst-color-navbar-link: 77, 77, 77;
  --pst-color-navbar-link-hover: var(--pst-color-active-navigation);
  --pst-color-navbar-link-active: var(--pst-color-active-navigation);
  --pst-color-sidebar-link: 77, 77, 77;
  --pst-color-sidebar-link-hover: var(--pst-color-active-navigation);
  --pst-color-sidebar-link-active: var(--pst-color-active-navigation);
  --pst-color-sidebar-expander-background-hover: 244, 244, 244;
  --pst-color-sidebar-caption: 77, 77, 77;
  --pst-color-toc-link: 119, 117, 122;
  --pst-color-toc-link-hover: var(--pst-color-active-navigation);
  --pst-color-toc-link-active: var(--pst-color-active-navigation);

  /*****************************************************************************
  * Icon
  **/

  /* font awesome icons*/
  --pst-icon-check-circle: "\f058";
  --pst-icon-info-circle: "\f05a";
  --pst-icon-exclamation-triangle: "\f071";
  --pst-icon-exclamation-circle: "\f06a";
  --pst-icon-times-circle: "\f057";
  --pst-icon-lightbulb: "\f0eb";

  /*****************************************************************************
  * Admonitions
  **/

  --pst-color-admonition-default: var(--pst-color-info);
  --pst-color-admonition-note: var(--pst-color-info);
  --pst-color-admonition-attention: var(--pst-color-warning);
  --pst-color-admonition-caution: var(--pst-color-warning);
  --pst-color-admonition-warning: var(--pst-color-warning);
  --pst-color-admonition-danger: var(--pst-color-danger);
  --pst-color-admonition-error: var(--pst-color-danger);
  --pst-color-admonition-hint: var(--pst-color-success);
  --pst-color-admonition-tip: var(--pst-color-success);
  --pst-color-admonition-important: var(--pst-color-success);

  --pst-icon-admonition-default: var(--pst-icon-info-circle);
  --pst-icon-admonition-note: var(--pst-icon-info-circle);
  --pst-icon-admonition-attention: var(--pst-icon-exclamation-circle);
  --pst-icon-admonition-caution: var(--pst-icon-exclamation-triangle);
  --pst-icon-admonition-warning: var(--pst-icon-exclamation-triangle);
  --pst-icon-admonition-danger: var(--pst-icon-exclamation-triangle);
  --pst-icon-admonition-error: var(--pst-icon-times-circle);
  --pst-icon-admonition-hint: var(--pst-icon-lightbulb);
  --pst-icon-admonition-tip: var(--pst-icon-lightbulb);
  --pst-icon-admonition-important: var(--pst-icon-exclamation-circle);

  /*****************************************************************************
  * versionmodified
  **/

  --pst-color-versionmodified-default: var(--pst-color-info);
  --pst-color-versionmodified-added: var(--pst-color-success);
  --pst-color-versionmodified-changed: var(--pst-color-warning);
  --pst-color-versionmodified-deprecated: var(--pst-color-danger);

  --pst-icon-versionmodified-default: var(--pst-icon-exclamation-circle);
  --pst-icon-versionmodified-added: var(--pst-icon-exclamation-circle);
  --pst-icon-versionmodified-changed: var(--pst-icon-exclamation-circle);
  --pst-icon-versionmodified-deprecated: var(--pst-icon-exclamation-circle);
}
