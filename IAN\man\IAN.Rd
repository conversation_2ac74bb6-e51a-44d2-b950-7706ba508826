% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN.R
\name{IAN}
\alias{IAN}
\title{Perform Intelligent Omics Analysis and Discovery - orchestrator}
\usage{
IAN(
  experimental_design = NULL,
  deseq_results = NULL,
  markeringroup = NULL,
  deg_file = NULL,
  gene_type = NULL,
  organism = NULL,
  input_type = NULL,
  pvalue = 0.05,
  ont = "BP",
  score_threshold = 0,
  output_dir = "enrichment_results",
  model = "gemini-1.5-flash-latest",
  temperature = 0,
  api_key_file = NULL
)
}
\arguments{
\item{experimental_design}{A character string describing the experimental design (optional, default is NULL).}

\item{deseq_results}{A data frame containing DESeq2 results (required if `input_type` is "deseq").}

\item{markeringroup}{A data frame containing marker genes (required if `input_type` is "findmarker", default is NULL).}

\item{deg_file}{Path to a file containing differentially expressed genes (required if `input_type` is "custom", default is NULL).}

\item{gene_type}{Character string specifying the gene identifier type in the input data. Must be one of "ENSEMBL", "ENTREZID", or "SYMBOL" (required if `input_type` is "deseq", "findmarker", or "custom", default is NULL).}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse" (required if `input_type` is "deseq", "findmarker", or "custom", default is NULL).}

\item{input_type}{Character string specifying the input type. Must be one of "findmarker", "deseq", or "custom" (default is NULL).}

\item{pvalue}{Numeric value specifying the p-value threshold for filtering enrichment results. Default is 0.05.}

\item{ont}{Character string specifying the GO ontology to use. Must be one of "BP" (biological process), "CC" (cellular component), or "MF" (molecular function). Default is "BP".}

\item{score_threshold}{Numeric value specifying the minimum combined score for STRING interactions. Default is 0.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}

\item{model}{Character string specifying the Gemini model to use. Default is "gemini-1.5-flash-latest".}

\item{temperature}{Numeric value controlling the randomness of the LLM response. Default is 0.}

\item{api_key_file}{Character string specifying the path to the file containing the Gemini API key (default is NULL).}
}
\value{
None (side effects: performs intelligent omics analysis and generates reports and visualizations).
}
\description{
This function performs an intelligent omics analysis and discovery, combining gene ID mapping, enrichment analysis,
multi-agent system execution, pathway comparison, network properties generation, network revision, system model generation, and visualization.
}
