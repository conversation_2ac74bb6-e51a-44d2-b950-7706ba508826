# 目标检测简介
## 目标检测概述
目标检测是计算机视觉的一个重要任务，根据整张图像内容进行描述，并结合目标物体的特征信息，确定该物体的类别与位置。不同于图像分类任务中我们只需要输出图像中主要物体对象的类标，在目标检测任务中，一张图像里往往含有多个物体对象，我们不仅需要输出这些物体的类标，同时还需要输出这些物体的位置，在表示位置时，我们一般采用目标检测边缘框`bounding box`进行表示，`bounding box`是一组坐标值，常见形式为`(x1,y1,x2,y2)`，其中`x1`代表物体左上横坐标，`y1`代表左上纵坐标，`x2`代表物体右下横坐标，`y2`代表物体右下纵坐标。
## 目标检测应用
目标检测技术不同于图像分类单一输出物体的种类，它将物体的位置和种类一起输出，这使得目标检测在一些领域有着重要的作用，目标检测常用于人脸检测、智慧交通、机器人、无人驾驶、遥感目标检测、安防领域检测异常、行人计数、安全系统等各大领域。
## 目标检测数据集
目标检测的数据集通常来说比图片分类的数据集小很多，因为每一张图片的标注的成本很高，相较于图片分类的常见标注方法是给定一个CSV文件（图片与标号一一对应）或者是给定一个文件夹（每个类对应一个子文件夹，对应标号的图片放在子文件夹下），但是对于目标检测来说因为一张图片中可能存在多个类，所以我们就不能放在子文件夹中，所以通常来说目标检测的数据集的标号需要额外存储，常见的存储格式有PASCAL VOC的格式和COCO的标注格式。假设使用文本文件存储的话，每一行表示一个物体，每一行分别由图片文件名（因为一张图片中可能有多个物体，所以同一个文件名可能会出现多次）、物体类别（标号）、边缘框（图片中物体的位置）组成，每一行一共有6（1+1+4）个值 
目标检测常用的数据集有PASCAL VOC2007, MS COCO
### COCO数据集
[[Dataset url](https://cocodataset.org/#home)]  [[Dataset paper](https://arxiv.org/pdf/1405.0312v3.pdf)] [[Dataset Benchmarks](https://paperswithcode.com/dataset/coco)]

COCO是目标检测中比较常见的数据集，类似于Imagenet在图片分类中的地位，coco数据集是微软开源的一个目标检测中常用的大规模数据集，相对于VOC而言，COCO数据集有着小目标多、单幅图片目标多、物体大多非中心分布、更符合日常环境的特点，因而COCO数据集的目标检测难度更大。对于目标检测任务，COCO包含80个类别，每年大赛的训练和和验证集包含120,000张图片，超过40,000张测试图片（每个图片包含多个物体）。下面是这个数据集中的80个类别：
|所属大类|lable|
|---|---|
|Person#1|person|
|Vehicle#8|bicycle,car,motorcycle,airplane,bus,train,truck,boat|
|Outdoor#5|traffic light, firhydrant, stop sign, parking meter, bench|
|Animal#10|bird,cat, dog,horse, sheep, cow, elephant, bear, zebra, giraffe|
|Accessory#5|backpack, umbrella,handbag, tie, suitcase|
|Sport#10|frisbee, skis,snowboard, sports ball, kite, baseball bat , baseball glove, skateboard, surfboard, tennisracket|
|Kitchen#7|bottle, wine glass,cup, fork, knife, spoon, bowl|
|Food#10|banana, apple,sandwich, orange, broccoli, carrot, hot dog, pizza, donut, cake|
|Furniture#6| chair, couch, potted plant,bed, dining table, toilet|
|Electronic#6|tv, laptop, mouse,remote, keyboard, cell phone|
|Appliance#5|microwave, oven,toaster, sink, refrigerator|
|Indoor#7|book, clock, vase,scissors, teddy bear, hair drier, toothbrus|

与PASCAL VOC数据标注格式不同，COCO的数据标注格式是以Json形式保存，具体形式可以参考COCO官网的[format data](https://cocodataset.org/#format-data)

### PASCAL VOC
[[Dataset url](http://host.robots.ox.ac.uk/pascal/VOC/index.html)]  [[Dataset paper](http://host.robots.ox.ac.uk/pascal/VOC/index.html)] [[Dataset Benchmarks](https://paperswithcode.com/dataset/pascal-voc-2007)]

Pascal VOC数据集是目标检测的常用的大规模数据集之一，从05年到12年都会举办比赛（比赛任务task： 分类Classification ，目标检测Object Detection，语义分割Class Segmentation，实例分割Object Segmentation，Action Classification（专注于人体动作的一种分类），Person Layout（专注于人体各部位的一种目标检测））。当前常见的数据集有VOC2007和VOC2012两个数据集。包含约10,000张带有边界框的图片用于训练和验证。含有20个类别。具体包括
|所属大类|label|
|---|---|
|Person| person|
|Animal| bird, cat, cow, dog, horse,sheep|
|Vehicle| aeroplane, bicycle, boat,bus, car, motorbike, train|
|Indoor|bottle, chair, dining table,potted plant, sofa, tv/monitor|

相较于COCO数据集的80类别，PASCAL VOC数据集仅有20类，因此也常被看作是目标检测领域的基准数据集。数据集下载完后会有五个文件夹：Annotations、ImageSets、JPEGImages、SegmentationClass、SegmentationObject
annotations：数据集标签的存储路径，通过XML文件格式，为图像数据存储各类任务的标签。其中部分标签为目标检测的标签。
JPEGImages：数据集图像的存储路径。
具体的标注格式，请参考[Annotation Guidelines](http://host.robots.ox.ac.uk/pascal/VOC/voc2007/guidelines.html)
### DOTA
[[Dataset url](https://captain-whu.github.io/DOTA/dataset.html)]  [[Dataset paper](https://captain-whu.github.io/DOTA/index.html)] [[Dataset Benchmarks](https://paperswithcode.com/dataset/dota)]

DOTA数据集是最为重要的遥感航空拍摄的目标检测数据集之一，数据来自Google Earth，GF-2和JL-1卫星，CycloMedia B.V。DOTA包含灰度图和RGB图像，并且每张图片以PNG形式保存，其中RGB来自于Google Earth和CycloMedia，灰度图来自GF-2和JL-1卫星。DOTA现有v1.0，v1.5，v2.0三个版本，分别支持15，16，18类的检测。

|DOTA 版本|包含类别|
|----|----|
|DOTA v1.0|plane, ship, storage tank, baseball diamond, tennis court, basketball court, ground track field, harbor, bridge, large vehicle, small vehicle, helicopter, roundabout, soccer ball field and swimming pool|
|DOTA v1.5|plane, ship, storage tank, baseball diamond, tennis court, basketball court, ground track field, harbor, bridge, large vehicle, small vehicle, helicopter, roundabout, soccer ball field, swimming pool and container crane.|
|DOTA v2.0|plane, ship, storage tank, baseball diamond, tennis court, basketball court, ground track field, harbor, bridge, large vehicle, small vehicle, helicopter, roundabout, soccer ball field, swimming pool, container crane, airport and helipad.|

关于DOTA的标注形式，请参考[DOTA Annotation format](https://captain-whu.github.io/DOTA/dataset.html)
### KITTI
[[Dataset url](http://www.cvlibs.net/datasets/kitti/)]  [[Dataset paper](https://arxiv.org/abs/1704.06857)] [[Dataset Benchmarks](https://paperswithcode.com/dataset/kitti)]
## 目标检测常用算法
随着算力的发展和深度学习的发展，目标检测经历了从基于手工设计特征的方法到基于深度学习提取特征的阶段。在早期，目标检测技术往往采用手工设计特征（Haar特征、梯度直方图HOG）加分类器（SVM、AdaBoost）的方法实现。随着卷积神经网络的发展，目标检测出现了一系列的基于卷积神经网络的目标检测技术，包括R-CNN系列,SSD系列,YOLO系列等。随着Transformer在自然语言处理和计算机视觉的发展，也出现了基于Transformer的目标检测技术，代表工作有DETR系列。在本部分，我们主要介绍基于深度学习的目标检测技术并进行部分代码的解读。

我们可以将基于深度学习的目标检测技术按照有无使用锚点框分为基于锚点框的目标检测方法（Anchor-based），无锚点框的目标检测方法（Anchor-free）和端到端的目标检测方法（Anchor-free）。其中端到端的目标检测技术也是属于特殊的无锚点框的目标检测方法。

我们可以将基于锚点框的目标检测方法分为单阶段目标检测方法（One-Stage）和两阶段目标检测方法（Two-Stage），其中单阶段目标检测方法代表作有YOLO (You Only Look Once)和SSD (Single Shot Detector)，两阶段目标检测方法的代表作有R-CNN（R-CNN，Fast RCNN,Faster RCNN，Mask RCNN，Cascade RCNN，SPPNet）系列。一般而言，两阶段目标检测具有较高的检测精度，而单阶段目标检测方法具有更高的精度。

同样，我们可以将无锚点框的目标检测方法分为基于目标点的目标检测方法和基于内部点的目标检测方法。
端到端的目标检测方法




