# 6.6 使用argparse进行调参
在深度学习中时，超参数的修改和保存是非常重要的一步，尤其是当我们在服务器上跑我们的模型时，如何更方便的修改超参数是我们需要考虑的一个问题。这时候，要是有一个库或者函数可以解析我们输入的命令行参数再传入模型的超参数中该多好。到底有没有这样的一种方法呢？答案是肯定的，这个就是 Python 标准库的一部分：Argparse。那么下面让我们看看他是多么方便。通过本节课，您将会收获以下内容
- argparse的简介
- argparse的使用
- 如何使用argparse修改超参数
## 6.6.1 argparse简介
argsparse是python的命令行解析的标准模块，内置于python，不需要安装。这个库可以让我们直接在命令行中就可以向程序中传入参数。我们可以使用`python file.py`来运行python文件。而argparse的作用就是将命令行传入的其他参数进行解析、保存和使用。在使用argparse后，我们在命令行输入的参数就可以以这种形式`python file.py --lr 1e-4 --batch_size 32`来完成对常见超参数的设置。

## 6.6.2 argparse的使用
总的来说，我们可以将argparse的使用归纳为以下三个步骤。
- 创建`ArgumentParser()`对象
- 调用`add_argument()`方法添加参数
- 使用`parse_args()`解析参数
在接下来的内容中，我们将以实际操作来学习argparse的使用方法。
```python
# demo.py
import argparse

# 创建ArgumentParser()对象
parser = argparse.ArgumentParser()

# 添加参数
parser.add_argument('-o', '--output', action='store_true', 
    help="shows output")
# action = `store_true` 会将output参数记录为True
# type 规定了参数的格式
# default 规定了默认值
parser.add_argument('--lr', type=float, default=3e-5, help='select the learning rate, default=1e-3') 

parser.add_argument('--batch_size', type=int, required=True, help='input batch size')  
# 使用parse_args()解析函数
args = parser.parse_args()

if args.output:
    print("This is some output")
    print(f"learning rate:{args.lr} ")

```
我们在命令行使用`python demo.py --lr 3e-4 --batch_size 32`，就可以看到以下的输出
```bash
This is some output
learning rate: 3e-4
```
argparse的参数主要可以分为可选参数和必选参数。可选参数就跟我们的`lr`参数相类似，未输入的情况下会设置为默认值。必选参数就跟我们的`batch_size`参数相类似，当我们给参数设置`required =True`后，我们就必须传入该参数，否则就会报错。看到我们的输入格式后，我们可能会有这样一个疑问，我输入参数的时候不使用--可以吗？答案是肯定的，不过我们需要在设置上做出一些改变。
```python
# positional.py
import argparse

# 位置参数
parser = argparse.ArgumentParser()

parser.add_argument('name')
parser.add_argument('age')

args = parser.parse_args()

print(f'{args.name} is {args.age} years old')
```
当我们不实用--后，将会严格按照参数位置进行解析。
```shell
$ positional_arg.py Peter 23
Peter is 23 years old
```
总的来说，argparse的使用很简单，以上这些操作就可以帮助我们进行参数的修改，在下面的部分，我将会分享我是如何在模型训练中使用argparse进行超参数的修改。
## 6.6.3 更加高效使用argparse修改超参数
每个人都有着不同的超参数管理方式，在这里我将分享我使用argparse管理超参数的方式，希望可以对大家有一些借鉴意义。通常情况下，为了使代码更加简洁和模块化，我一般会将有关超参数的操作写在`config.py`，然后在`train.py`或者其他文件导入就可以。具体的`config.py`可以参考如下内容。
```python
import argparse  
  
def get_options(parser=argparse.ArgumentParser()):  
  
    parser.add_argument('--workers', type=int, default=0,  
                        help='number of data loading workers, you had better put it '  
                              '4 times of your gpu')  
  
    parser.add_argument('--batch_size', type=int, default=4, help='input batch size, default=64')  
  
    parser.add_argument('--niter', type=int, default=10, help='number of epochs to train for, default=10')  
  
    parser.add_argument('--lr', type=float, default=3e-5, help='select the learning rate, default=1e-3')  
  
    parser.add_argument('--seed', type=int, default=118, help="random seed")  
  
    parser.add_argument('--cuda', action='store_true', default=True, help='enables cuda')  
    parser.add_argument('--checkpoint_path',type=str,default='',  
                        help='Path to load a previous trained model if not empty (default empty)')  
    parser.add_argument('--output',action='store_true',default=True,help="shows output")  
  
    opt = parser.parse_args()  
  
    if opt.output:  
        print(f'num_workers: {opt.workers}')  
        print(f'batch_size: {opt.batch_size}')  
        print(f'epochs (niters) : {opt.niter}')  
        print(f'learning rate : {opt.lr}')  
        print(f'manual_seed: {opt.seed}')  
        print(f'cuda enable: {opt.cuda}')  
        print(f'checkpoint_path: {opt.checkpoint_path}')  
  
    return opt  
  
if __name__ == '__main__':  
    opt = get_options()
```

```shell
$ python config.py

num_workers: 0
batch_size: 4
epochs (niters) : 10
learning rate : 3e-05
manual_seed: 118
cuda enable: True
checkpoint_path:
```

随后在`train.py`等其他文件，我们就可以使用下面的这样的结构来调用参数。
```python
# 导入必要库
...
import config

opt = config.get_options()

manual_seed = opt.seed
num_workers = opt.workers
batch_size = opt.batch_size
lr = opt.lr
niters = opt.niters
checkpoint_path = opt.checkpoint_path

# 随机数的设置，保证复现结果
def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True

...


if __name__ == '__main__':
	set_seed(manual_seed)
	for epoch in range(niters):
		train(model,lr,batch_size,num_workers,checkpoint_path)
		val(model,lr,batch_size,num_workers,checkpoint_path)

```
## 总结
argparse给我们提供了一种新的更加便捷的方式，而在一些大型的深度学习库中人们也会使用json、dict、yaml等文件格式去保存超参数进行训练。如果大家还想进一步的了解argparse的使用，大家可以点击下面提供的连接进行更深的学习和了解。
1. [Python argparse 教程](https://geek-docs.com/python/python-tutorial/python-argparse.html)
2. [argparse 官方教程](https://docs.python.org/3/library/argparse.html)