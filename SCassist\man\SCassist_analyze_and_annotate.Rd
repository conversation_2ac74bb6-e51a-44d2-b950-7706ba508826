% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/analyze_and_annotate.R
\name{SCassist_analyze_and_annotate}
\alias{SCassist_analyze_and_annotate}
\title{Analyze Top Markers, Predict Cell Types, and Optionally Annotate a Seurat Object}
\usage{
SCassist_analyze_and_annotate(llm_server="google",
                             seurat_object_name = NULL,
                             all_markers,
                             top_genes = 30,
                             temperature = 0,
                             max_output_tokens = 10048,
                             model_G = "gemini-1.5-flash-latest",
                             model_O = "llama3",
                             model_C = "gpt-4o-mini",
                             api_key_file = "api_keys.txt",
                             model_params = list(seed = 42,
                                   temperature = 0, num_gpu = 0)
                             )
}
\arguments{
\item{llm_server}{The LLM server to use. Options are "google" or "ollama" or "openai". Default is "google".}

\item{seurat_object_name}{(optional) The name of the Seurat object to annotate.
If provided, the function will annotate the object with predicted cell types.
Default is NULL, which skips annotation.}

\item{all_markers}{A data frame containing markers for each cluster, as output
by the `FindAllMarkers` function from the Seurat package. This data frame should
have columns for cluster, gene, avg_logFC, p_val, and pct.1.}

\item{top_genes}{The number of top markers to consider for each cluster. This
parameter controls the number of genes used as input for the LLM. A higher
value will provide more information to the LLM, but may also increase the
processing time.}

\item{temperature}{A value between 0 and 1 that controls the creativity of the LLM's response.
Lower values produce more deterministic results, while higher values allow for
more diverse and potentially unexpected predictions.}

\item{max_output_tokens}{The maximum number of tokens the LLM can generate in its response.
This parameter limits the length of the LLM's output. A higher value allows for
more detailed explanations, but may also increase the processing time.}

\item{model_G}{Character string specifying the Gemini model to use for
analysis. Default is "gemini-1.5-flash-latest".}

\item{model_O}{Character string specifying the Ollama model to use for
analysis. Default is "llama3".}

\item{model_C}{Character string specifying the OpenAI model to use for
analysis. Default is "gpt-4o-mini".}

\item{api_key_file}{The path to a file containing your Gemini API key. This key
is required to access the Gemini API. You can obtain a key from Google Cloud
Platform.}

\item{model_params}{A list of parameters to be passed to the `ollama::query` function.
This allows customization of the Llama model's behavior. Default is `list(seed = 42, temperature = 0, num_gpu = 0)`.}
}
\value{
Returns a data frame containing the predicted cell types and reasoning
 for each cluster (`sca_annotation`). The data frame has three columns:
 - `cluster_num`: The cluster number.
 - `cell_type`: The predicted cell type.
 - `scassist_reasoning`: A brief explanation of the reasoning behind the prediction.

 The function also updates the Seurat object in place if `seurat_object_name` is provided.
 The updated object will have a new column named `scassist_annotation_` followed by the
 name of the current identity column, containing the predicted cell types for each cell.
}
\description{
This function performs a comprehensive analysis of single-cell data, including:
1. **Analyzing top markers for each cluster:** It identifies the most highly expressed genes (markers) in each cluster of cells.
2. **Predicting potential cell types:** It uses a large language model (LLM) to analyze the top markers and predict the cell type that is most likely represented by each cluster.
3. **Optionally annotating a Seurat object:** If you provide the name of a Seurat object, the function will add a new column to the object's metadata containing the predicted cell types for each cell.

**Important Note:** The function assumes that the current identity of the Seurat
object (set with `Idents(seurat_object)`) is the same as the identity used
when running `FindAllMarkers`. This ensures correct cluster annotation.
}
\details{
This function was written with assistance from Google's Gemini.
}
\examples{
\dontrun{
# Assuming you have a Seurat object named 'seurat_obj' and a data frame named 'markers'
# containing the results of FindAllMarkers
SCassist_analyze_and_annotate(all_markers = markers,
                             seurat_object_name = "seurat_obj",
                             api_key_file = "my_api_key.txt")
}
}
\author{
Vijay Nagarajan, PhD, NEI/NIH
}
\keyword{LLM,}
\keyword{Seurat,}
\keyword{analysis,}
\keyword{annotation}
\keyword{cell}
\keyword{prediction,}
\keyword{single-cell}
\keyword{type}
