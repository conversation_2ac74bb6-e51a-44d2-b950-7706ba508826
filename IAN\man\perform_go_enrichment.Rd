% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_enrichment_analysis.R
\name{perform_go_enrichment}
\alias{perform_go_enrichment}
\title{Perform GO Enrichment Analysis}
\usage{
perform_go_enrichment(
  gene_ids,
  gene_mapping,
  organism,
  ont = "BP",
  pvalue = 0.05,
  output_dir = "enrichment_results"
)
}
\arguments{
\item{gene_ids}{A vector of gene identifiers (ENTREZIDs).}

\item{gene_mapping}{A data frame containing gene mappings between ENTREZID and SYMBOL. Must have columns named "ENTREZ<PERSON>" and "SYMB<PERSON>".}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse".}

\item{ont}{Character string specifying the GO ontology to use. Must be one of "BP" (biological process), "CC" (cellular component), or "MF" (molecular function). Default is "BP".}

\item{pvalue}{Numeric value specifying the p-value threshold for filtering results. Default is 0.05.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A data frame containing the filtered GO enrichment results, or NULL if the analysis fails.
}
\description{
Performs Gene Ontology (GO) enrichment analysis using the `enrichGO` function from the `clusterProfiler` package.
}
