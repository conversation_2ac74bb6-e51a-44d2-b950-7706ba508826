% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/analyze_pcs.R
\name{SCassist_analyze_pcs}
\alias{SCassist_analyze_pcs}
\title{Analyze Top Principal Components (PCs) with a Large Language Model (LLM)}
\usage{
SCassist_analyze_pcs(llm_server="google",
                   seurat_object_name, num_pcs = 5, 
                   top_n_pc_contributing_genes = 50, 
                   experimental_design = "",
                   temperature = 0,
                   max_output_tokens = 10048,
                   model_G = "gemini-1.5-flash-latest",
                   model_O = "llama3",
                   model_C = "gpt-4o-mini",
                   api_key_file = "api_keys.txt",
                   model_params = list(seed = 42, temperature = 0, num_gpu = 0))
}
\arguments{
\item{llm_server}{The LLM server to use. Options are "google" or "ollama" or "openai". Default is "google".}

\item{seurat_object_name}{The name of the Seurat object containing the
single-cell RNA-seq data. The object should be accessible in the current
environment and should have PCA already run (e.g., using `RunPCA`).}

\item{num_pcs}{The number of top PCs to analyze. Defaults to 6.}

\item{top_n_pc_contributing_genes}{The number of top genes contributing to
each PC to include in the analysis. Defaults to 50.}

\item{experimental_design}{(Optional) A character string describing the 
experimental design. This information helps the LLM contextualize the 
analysis. If not provided, the LLM will analyze the data without specific
experimental context.}

\item{temperature}{Controls the creativity of the LLM's response. Lower values 
produce more deterministic results. Defaults to 0.}

\item{max_output_tokens}{The maximum number of tokens the LLM can generate. Defaults to 10048.}

\item{model_G}{Character string specifying the Gemini model to use for
analysis. Default is "gemini-1.5-flash-latest".}

\item{model_O}{Character string specifying the Ollama model to use for
analysis. Default is "llama3".}

\item{model_C}{Character string specifying the OpenAI model to use for
analysis. Default is "gpt-4o-mini".}

\item{api_key_file}{The path to the file containing the API key for the LLM. Defaults to "api_keys.txt".}

\item{model_params}{A list of parameters to be passed to the `ollama::query` function.
This allows customization of the Llama model's behavior. Default is `list(seed = 42, temperature = 0, num_gpu = 0)`.}
}
\value{
A character string containing the LLM's analysis of the top PCs,
 including interpretations of the biological processes driving the variations,
 lists of top contributing genes and their associated pathways, and a summary
 paragraph.
}
\description{
This function analyzes the top principal components (PCs) from a Seurat object
using a large language model (LLM) to identify potential biological processes
driving the variations captured by each PC. The LLM considers the top genes
contributing to each PC and their associated pathways to provide insights
into the underlying biological system. This version analyzes a specified number of PCs
and then combines the summaries from the LLM for each PC into a single overall summary.
}
\details{
This function was written with assistance from Google's Gemini.
}
\author{
Vijay Nagarajan, PhD, NEI/NIH
}
\keyword{analyze,}
\keyword{components,}
\keyword{language}
\keyword{large}
\keyword{llm}
\keyword{model,}
\keyword{pca,}
\keyword{principal}
