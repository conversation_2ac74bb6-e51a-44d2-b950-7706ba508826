% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/recommend_pcs.R
\name{SCassist_recommend_pcs}
\alias{SCassist_recommend_pcs}
\title{Recommend Number of Principal Components (PCs) Using an LLM}
\usage{
SCassist_recommend_pcs(llm_server="google",
 seurat_object_name, 
 experimental_design = NULL,
 temperature = 0,
 max_output_tokens = 10048,
 model_G = "gemini-1.5-flash-latest",
 model_O = "llama3",
 model_C = "gpt-4o-mini",
 api_key_file = "api_keys.txt",
 model_params = list(seed = 42, temperature = 0, num_gpu = 0))
}
\arguments{
\item{llm_server}{The LLM server to use. Options are "google" or "ollama" or "openai". Default is "google".}

\item{seurat_object_name}{The name of the Seurat object containing the
single-cell RNA-seq data. The object should be accessible in the current
environment and should have PCA already run (e.g., using `RunPCA`).}

\item{experimental_design}{(Optional) A character string describing the experimental
design. This information helps the LLM contextualize the analysis. If not provided,
the prompt will not include the experimental design information.}

\item{temperature}{Numeric value between 0 and 1, controlling the
"creativity" of the Gemini model's response. Higher values lead to more
diverse and potentially unexpected outputs. Default is 0.}

\item{max_output_tokens}{Integer specifying the maximum number of tokens
the Gemini model can generate in its response. Default is 10048.}

\item{model_G}{Character string specifying the Gemini model to use for
analysis. Default is "gemini-1.5-flash-latest".}

\item{model_O}{Character string specifying the Ollama model to use for
analysis. Default is "llama3".}

\item{model_C}{Character string specifying the OpenAI model to use for
analysis. Default is "gpt-4o-mini".}

\item{api_key_file}{Character string specifying the path to a file containing
the API key for accessing the Gemini model.}

\item{model_params}{A list of parameters to be passed to the `ollama::query` function.
This allows customization of the Llama model's behavior. Default is `list(seed = 42, temperature = 0, num_gpu = 0)`.}
}
\value{
A numeric value representing the recommended number of PCs to use
 for downstream analysis.
}
\description{
This function analyzes the variance explained by each principal component
(PC) from a PCA analysis using a large language model (LLM) to identify
the "elbow point" in the scree plot. Based on this elbow point, the LLM
recommends the number of PCs to use for downstream analysis, such as
finding neighbors or running UMAP.
}
\details{
This function was written with assistance from Google's Gemini and Meta's Llama3.
}
\examples{
\dontrun{
# Assuming you have a Seurat object named 'seurat_obj' with PCA run
SCassist_recommend_pcs(seurat_object_name = "seurat_obj",
                       experimental_design = "Time course experiment",
                       api_key_file = "my_api_key.txt")
}
}
\author{
Vijay Nagarajan, PhD, NEI/NIH
}
\keyword{LLM,}
\keyword{PCA,}
\keyword{dimensionality}
\keyword{recommendation}
\keyword{reduction,}
\keyword{single-cell,}
