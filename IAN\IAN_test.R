# Install the necessary packages
install.packages(c("dplyr", "stringr", "readr", "R6", "future", "furrr", "progressr", "httr", "plyr", "rmarkdown", "visNetwork", "igraph", "devtools", "ggh4x", "openxlsx", "tidyverse","kableExtra"))
BiocManager::install(c("clusterProfiler", "ReactomePA", "org.Hs.eg.db", "org.Mm.eg.db", "STRINGdb"))
install.packages("enrichR")
BiocManager::install("STRINGdb")
library(STRINGdb)
# Install the devtools package if you don't have it
install.packages("devtools")
# Install IAN from GitHub
devtools::install_github("NIH-NEI/IAN")






# Load the IAN package and enrichR library
library(IAN)
library(enrichR)

# Define the path to your Google Gemini API key file
api_key_file <- "D:/pytorch_study/IAN/gemini_api_key.txt"

# Define the path to the DEG file, that you downloaded from the above example data link
deg_file_path <- "D:/pytorch_study/IAN/inst/docs/uveitis-PIIS0002939421000271-deg.txt"

# Call the function with basic parameters
IAN(
  deg_file = deg_file_path,
  gene_type = "ENSEMBL",
  organism = "human",
  input_type = "custom",
  output_dir = "IAN_results",
  api_key_file = api_key_file
)





