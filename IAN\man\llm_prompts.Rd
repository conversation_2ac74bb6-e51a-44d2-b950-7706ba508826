% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_llm_prompts.R
\name{llm_prompts}
\alias{llm_prompts}
\alias{create_llm_prompt_wp}
\title{IAN_llm_prompts.R}
\usage{
create_llm_prompt_wp(
  enrichment_results,
  analysis_type,
  chea_results = NULL,
  string_results = NULL,
  gene_symbols = NULL,
  string_network_properties = NULL,
  go_results = NULL,
  experimental_design = NULL
)
}
\arguments{
\item{enrichment_results}{A data frame containing WikiPathways enrichment results.}

\item{analysis_type}{Character string specifying the type of analysis (e.g., "WikiPathways").}

\item{chea_results}{A data frame containing ChEA transcription factor enrichment results (optional).}

\item{string_results}{A list containing STRING protein-protein interaction data (optional).}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{string_network_properties}{A data frame containing STRING network properties (optional).}

\item{go_results}{A data frame containing Gene Ontology (GO) enrichment results (optional).}

\item{experimental_design}{A character string describing the experimental design (optional).}
}
\value{
A character string containing the LLM prompt.
}
\description{
This script defines functions to create prompts for Large Language Models (LLMs) based on various enrichment analysis results.
The prompts are designed to guide the LLM in analyzing the data and generating insights about the underlying biological mechanisms.
}
\section{Functions}{

\describe{
  \item{\code{\link{create_llm_prompt_wp}}}{: Creates an LLM prompt for WikiPathways enrichment analysis.}
  \item{\code{\link{create_llm_prompt_kegg}}}{: Creates an LLM prompt for KEGG enrichment analysis.}
  \item{\code{\link{create_llm_prompt_reactome}}}{: Creates an LLM prompt for Reactome enrichment analysis.}
  \item{\code{\link{create_llm_prompt_chea}}}{: Creates an LLM prompt for ChEA enrichment analysis.}
  \item{\code{\link{create_llm_prompt_go}}}{: Creates an LLM prompt for GO enrichment analysis.}
  \item{\code{\link{create_llm_prompt_string}}}{: Creates an LLM prompt for STRING interaction analysis.}
}
}

