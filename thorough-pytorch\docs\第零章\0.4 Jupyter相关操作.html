
<!DOCTYPE html>

<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

    <title>Jupyter notebook/Lab 简述 &#8212; 深入浅出PyTorch</title>
    
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">
<link href="../_static/styles/pydata-sphinx-theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">

    
  <link rel="stylesheet"
    href="../_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" href="../_static/styles/sphinx-book-theme.css?digest=62ba249389abaaa9ffc34bf36a076bdc1d65ee18" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../_static/togglebutton.css" />
    <link rel="stylesheet" type="text/css" href="../_static/mystnb.css" />
    <link rel="stylesheet" type="text/css" href="../_static/plot_directive.css" />
    
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf">

    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/doctools.js"></script>
    <script>let toggleHintShow = 'Click to show';</script>
    <script>let toggleHintHide = 'Click to hide';</script>
    <script>let toggleOpenOnPrint = 'true';</script>
    <script src="../_static/togglebutton.js"></script>
    <script src="../_static/scripts/sphinx-book-theme.js?digest=f31d14ad54b65d19161ba51d4ffff3a77ae00456"></script>
    <script>var togglebuttonSelector = '.toggle, .admonition.dropdown, .tag_hide_input div.cell_input, .tag_hide-input div.cell_input, .tag_hide_output div.cell_output, .tag_hide-output div.cell_output, .tag_hide_cell.cell, .tag_hide-cell.cell';</script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="第一章：PyTorch的简介和安装" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/index.html" />
    <link rel="prev" title="常用包的学习" href="0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="docsearch:language" content="zh">
    

    <!-- Google Analytics -->
    
  </head>
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="60">
<!-- Checkboxes to toggle the left sidebar -->
<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation" aria-label="Toggle navigation sidebar">
<label class="overlay overlay-navbar" for="__navigation">
    <div class="visually-hidden">Toggle navigation sidebar</div>
</label>
<!-- Checkboxes to toggle the in-page toc -->
<input type="checkbox" class="sidebar-toggle" name="__page-toc" id="__page-toc" aria-label="Toggle in-page Table of Contents">
<label class="overlay overlay-pagetoc" for="__page-toc">
    <div class="visually-hidden">Toggle in-page Table of Contents</div>
</label>
<!-- Headers at the top -->
<div class="announcement header-item noprint"></div>
<div class="header header-item noprint"></div>

    
    <div class="container-fluid" id="banner"></div>

    

    <div class="container-xl">
      <div class="row">
          
<!-- Sidebar -->
<div class="bd-sidebar noprint" id="site-navigation">
    <div class="bd-sidebar__content">
        <div class="bd-sidebar__top"><div class="navbar-brand-box">
    <a class="navbar-brand text-wrap" href="../index.html">
      
      
      
      <h1 class="site-logo" id="site-title">深入浅出PyTorch</h1>
      
    </a>
</div><form class="bd-search d-flex align-items-center" action="../search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main">
    <div class="bd-toc-item active">
        <p aria-level="2" class="caption" role="heading">
 <span class="caption-text">
  目录
 </span>
</p>
<ul class="current nav bd-sidenav">
 <li class="toctree-l1 current active has-children">
  <a class="reference internal" href="index.html">
   第零章：前置知识
  </a>
  <input checked="" class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" type="checkbox"/>
  <label for="toctree-checkbox-1">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul class="current">
   <li class="toctree-l2">
    <a class="reference internal" href="0.1%20%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%AE%80%E5%8F%B2.html">
     人工智能简史
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="0.2%20%E8%AF%84%E4%BB%B7%E6%8C%87%E6%A0%87.html">
     模型评价指标
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html">
     常用包的学习
    </a>
   </li>
   <li class="toctree-l2 current active">
    <a class="current reference internal" href="#">
     Jupyter notebook/Lab 简述
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/index.html">
   第一章：PyTorch的简介和安装
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-2" name="toctree-checkbox-2" type="checkbox"/>
  <label for="toctree-checkbox-2">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.1%20PyTorch%E7%AE%80%E4%BB%8B.html">
     1.1 PyTorch简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.2%20PyTorch%E7%9A%84%E5%AE%89%E8%A3%85.html">
     1.2 PyTorch的安装
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html">
     1.3 PyTorch相关资源
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/index.html">
   第二章：PyTorch基础知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-3" name="toctree-checkbox-3" type="checkbox"/>
  <label for="toctree-checkbox-3">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.1%20%E5%BC%A0%E9%87%8F.html">
     2.1 张量
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.2%20%E8%87%AA%E5%8A%A8%E6%B1%82%E5%AF%BC.html">
     2.2 自动求导
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.3%20%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%E7%AE%80%E4%BB%8B.html">
     2.3 并行计算简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.4%20AI%E7%A1%AC%E4%BB%B6%E5%8A%A0%E9%80%9F%E8%AE%BE%E5%A4%87.html">
     AI硬件加速设备
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/index.html">
   第三章：PyTorch的主要组成模块
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-4" name="toctree-checkbox-4" type="checkbox"/>
  <label for="toctree-checkbox-4">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.1%20%E6%80%9D%E8%80%83%EF%BC%9A%E5%AE%8C%E6%88%90%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E7%9A%84%E5%BF%85%E8%A6%81%E9%83%A8%E5%88%86.html">
     3.1 思考：完成深度学习的必要部分
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.2%20%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE.html">
     3.2 基本配置
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.3%20%E6%95%B0%E6%8D%AE%E8%AF%BB%E5%85%A5.html">
     3.3 数据读入
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.4%20%E6%A8%A1%E5%9E%8B%E6%9E%84%E5%BB%BA.html">
     3.4 模型构建
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.5%20%E6%A8%A1%E5%9E%8B%E5%88%9D%E5%A7%8B%E5%8C%96.html">
     3.5 模型初始化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.6%20%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     3.6 损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.7%20%E8%AE%AD%E7%BB%83%E4%B8%8E%E8%AF%84%E4%BC%B0.html">
     3.7 训练和评估
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.8%20%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     3.8 可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.9%20%E4%BC%98%E5%8C%96%E5%99%A8.html">
     3.9 PyTorch优化器
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/index.html">
   第四章：PyTorch基础实战
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-5" name="toctree-checkbox-5" type="checkbox"/>
  <label for="toctree-checkbox-5">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.1%20ResNet.html">
     4.1 ResNet
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.4%20FashionMNIST%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     基础实战——FashionMNIST时装分类
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/index.html">
   第五章：PyTorch模型定义
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-6" name="toctree-checkbox-6" type="checkbox"/>
  <label for="toctree-checkbox-6">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.1%20PyTorch%E6%A8%A1%E5%9E%8B%E5%AE%9A%E4%B9%89%E7%9A%84%E6%96%B9%E5%BC%8F.html">
     5.1 PyTorch模型定义的方式
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.2%20%E5%88%A9%E7%94%A8%E6%A8%A1%E5%9E%8B%E5%9D%97%E5%BF%AB%E9%80%9F%E6%90%AD%E5%BB%BA%E5%A4%8D%E6%9D%82%E7%BD%91%E7%BB%9C.html">
     5.2 利用模型块快速搭建复杂网络
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.3%20PyTorch%E4%BF%AE%E6%94%B9%E6%A8%A1%E5%9E%8B.html">
     5.3 PyTorch修改模型
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.4%20PyTorh%E6%A8%A1%E5%9E%8B%E4%BF%9D%E5%AD%98%E4%B8%8E%E8%AF%BB%E5%8F%96.html">
     5.4 PyTorch模型保存与读取
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/index.html">
   第六章：PyTorch进阶训练技巧
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-7" name="toctree-checkbox-7" type="checkbox"/>
  <label for="toctree-checkbox-7">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.1%20%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     6.1 自定义损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.2%20%E5%8A%A8%E6%80%81%E8%B0%83%E6%95%B4%E5%AD%A6%E4%B9%A0%E7%8E%87.html">
     6.2 动态调整学习率
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-torchvision.html">
     6.3 模型微调-torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-timm.html">
     6.3 模型微调 - timm
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.4%20%E5%8D%8A%E7%B2%BE%E5%BA%A6%E8%AE%AD%E7%BB%83.html">
     6.4 半精度训练
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.5%20%E6%95%B0%E6%8D%AE%E5%A2%9E%E5%BC%BA-imgaug.html">
     6.5 数据增强-imgaug
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.6%20%E4%BD%BF%E7%94%A8argparse%E8%BF%9B%E8%A1%8C%E8%B0%83%E5%8F%82.html">
     6.6 使用argparse进行调参
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/index.html">
   第七章：PyTorch可视化
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-8" name="toctree-checkbox-8" type="checkbox"/>
  <label for="toctree-checkbox-8">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.1%20%E5%8F%AF%E8%A7%86%E5%8C%96%E7%BD%91%E7%BB%9C%E7%BB%93%E6%9E%84.html">
     7.1 可视化网络结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     7.2 CNN可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.3%20%E4%BD%BF%E7%94%A8TensorBoard%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.3 使用TensorBoard可视化训练过程
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.4%20%E4%BD%BF%E7%94%A8wandb%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.4 使用wandb可视化训练过程
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/index.html">
   第八章：PyTorch生态简介
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-9" name="toctree-checkbox-9" type="checkbox"/>
  <label for="toctree-checkbox-9">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.1%20%E6%9C%AC%E7%AB%A0%E7%AE%80%E4%BB%8B.html">
     8.1 本章简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html">
     8.2 torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.3%20%E8%A7%86%E9%A2%91%20-%20PyTorchVideo.html">
     8.3 PyTorchVideo简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html">
     8.4 torchtext简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.5%20%E9%9F%B3%E9%A2%91%20-%20torchaudio.html">
     8.5 torchaudio简介
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/index.html">
   第九章：PyTorch的模型部署
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-10" name="toctree-checkbox-10" type="checkbox"/>
  <label for="toctree-checkbox-10">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/9.1%20%E4%BD%BF%E7%94%A8ONNX%E8%BF%9B%E8%A1%8C%E9%83%A8%E7%BD%B2%E5%B9%B6%E6%8E%A8%E7%90%86.html">
     9.1 使用ONNX进行部署并推理
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/index.html">
   第十章：常见代码解读
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-11" name="toctree-checkbox-11" type="checkbox"/>
  <label for="toctree-checkbox-11">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.1%20%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     10.1 图像分类简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.2%20%E7%9B%AE%E6%A0%87%E6%A3%80%E6%B5%8B.html">
     目标检测简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.3%20%E5%9B%BE%E5%83%8F%E5%88%86%E5%89%B2.html">
     10.3 图像分割简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ResNet%E6%BA%90%E7%A0%81%E8%A7%A3%E8%AF%BB.html">
     ResNet源码解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/RNN%E8%AF%A6%E8%A7%A3%E5%8F%8A%E5%85%B6%E5%AE%9E%E7%8E%B0.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/LSTM%E8%A7%A3%E8%AF%BB%E5%8F%8A%E5%AE%9E%E6%88%98.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Transformer%20%E8%A7%A3%E8%AF%BB.html">
     Transformer 解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ViT%E8%A7%A3%E8%AF%BB.html">
     ViT解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Swin-Transformer%E8%A7%A3%E8%AF%BB.html">
     Swin Transformer解读
    </a>
   </li>
  </ul>
 </li>
</ul>

    </div>
</nav></div>
        <div class="bd-sidebar__bottom">
             <!-- To handle the deprecated key -->
            
            <div class="navbar_extra_footer">
            Theme by the <a href="https://ebp.jupyterbook.org">Executable Book Project</a>
            </div>
            
        </div>
    </div>
    <div id="rtd-footer-container"></div>
</div>


          


          
<!-- A tiny helper pixel to detect if we've scrolled -->
<div class="sbt-scroll-pixel-helper"></div>
<!-- Main content -->
<div class="col py-0 content-container">
    
    <div class="header-article row sticky-top noprint">
        



<div class="col py-1 d-flex header-article-main">
    <div class="header-article__left">
        
        <label for="__navigation"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="right"
title="Toggle navigation"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-bars"></i>
  </span>

</label>

        
    </div>
    <div class="header-article__right">
<button onclick="toggleFullScreen()"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="bottom"
title="Fullscreen mode"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-expand"></i>
  </span>

</button>

<div class="menu-dropdown menu-dropdown-repository-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Source repositories">
      <i class="fab fa-github"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Source repository"
>
  

<span class="headerbtn__icon-container">
  <i class="fab fa-github"></i>
  </span>
<span class="headerbtn__text-container">repository</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/issues/new?title=Issue%20on%20page%20%2F第零章/0.4 Jupyter相关操作.html&body=Your%20issue%20content%20here."
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Open an issue"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-lightbulb"></i>
  </span>
<span class="headerbtn__text-container">open issue</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/edit/master/第零章/0.4 Jupyter相关操作.md"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Edit this page"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-pencil-alt"></i>
  </span>
<span class="headerbtn__text-container">suggest edit</span>
</a>

      </li>
      
    </ul>
  </div>
</div>

<div class="menu-dropdown menu-dropdown-download-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Download this page">
      <i class="fas fa-download"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="../_sources/第零章/0.4 Jupyter相关操作.md.txt"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Download source file"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file"></i>
  </span>
<span class="headerbtn__text-container">.md</span>
</a>

      </li>
      
      <li>
        
<button onclick="printPdf(this)"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="left"
title="Print to PDF"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file-pdf"></i>
  </span>
<span class="headerbtn__text-container">.pdf</span>
</button>

      </li>
      
    </ul>
  </div>
</div>
<label for="__page-toc"
  class="headerbtn headerbtn-page-toc"
  
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-list"></i>
  </span>

</label>

    </div>
</div>

<!-- Table of contents -->
<div class="col-md-3 bd-toc show noprint">
    <div class="tocsection onthispage pt-5 pb-3">
        <i class="fas fa-list"></i> Contents
    </div>
    <nav id="bd-toc-nav" aria-label="Page">
        <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id1">
   1 Jupyter Notebook/Lab安装
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id2">
   2 Jupyter Notebook/Lab配置
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id3">
     2.1 设置文件存放位置
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id4">
     2.2 使用虚拟环境
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id5">
   3 Jupyter Notebook\Lab基本操作
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id6">
     3.1 基本使用
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#jupyter-notebook">
       3.1.1 Jupyter Notebook
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#jupyter-lab">
       3.1.2 Jupyter Lab
      </a>
     </li>
    </ul>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id7">
     3.2 常用快捷键
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id8">
     3.3 安装插件
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id9">
   4 进阶操作
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id10">
   5 参考资料
  </a>
 </li>
</ul>

    </nav>
</div>
    </div>
    <div class="article row">
        <div class="col pl-md-3 pl-lg-5 content-container">
            <!-- Table of contents that is only displayed when printing the page -->
            <div id="jb-print-docs-body" class="onlyprint">
                <h1>Jupyter notebook/Lab 简述</h1>
                <!-- Table of contents -->
                <div id="print-main-content">
                    <div id="jb-print-toc">
                        
                        <div>
                            <h2> Contents </h2>
                        </div>
                        <nav aria-label="Page">
                            <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id1">
   1 Jupyter Notebook/Lab安装
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id2">
   2 Jupyter Notebook/Lab配置
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id3">
     2.1 设置文件存放位置
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id4">
     2.2 使用虚拟环境
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id5">
   3 Jupyter Notebook\Lab基本操作
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id6">
     3.1 基本使用
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#jupyter-notebook">
       3.1.1 Jupyter Notebook
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#jupyter-lab">
       3.1.2 Jupyter Lab
      </a>
     </li>
    </ul>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id7">
     3.2 常用快捷键
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id8">
     3.3 安装插件
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id9">
   4 进阶操作
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id10">
   5 参考资料
  </a>
 </li>
</ul>

                        </nav>
                    </div>
                </div>
            </div>
            <main id="main-content" role="main">
                
              <div>
                
  <section class="tex2jax_ignore mathjax_ignore" id="jupyter-notebook-lab">
<h1>Jupyter notebook/Lab 简述<a class="headerlink" href="#jupyter-notebook-lab" title="永久链接至标题">#</a></h1>
<p>在数据科学，机器学习，深度学习中，我们希望即时进行图像的可视化和函数的计算，基于这种需求，人们开发出了基于网页的用于交互计算的应用程序Jupyter Notebook。在我们的教程代码也是基于Jupyter notebook。除此之外，它们可被应用于全过程计算：开发、文档编写、运行代码和展示结果。在Jupyter Notebook中编写的文档保存为<code class="docutils literal notranslate"><span class="pre">.ipynb</span></code>的<code class="docutils literal notranslate"><span class="pre">JSON</span></code>格式文件，文档可以导出为HTML、LaTeX、markdown、PDF等格式。Jupyter Notebook的主要特点有：</p>
<ul class="simple">
<li><p>编程时具有<strong>语法高亮</strong>、<em>缩进</em>、<em>tab补全</em>的功能。</p></li>
<li><p>可直接通过浏览器运行代码，同时在代码块下方展示运行结果。</p></li>
<li><p>以富媒体格式展示计算结果。富媒体格式包括：HTML，LaTeX，PNG，SVG等。</p></li>
<li><p>对代码编写说明文档或语句时，支持Markdown语法。</p></li>
<li><p>支持使用LaTeX编写数学性说明。</p></li>
</ul>
<p>除此之外，好用的Jupyter Notebook还有个“双胞胎”——Jupyter Lab。Jupyter Lab是基于Web的集成开发环境，可以把它当作进化版的Jupyter Notebook。使用Jupyter Lab可以同时在一个浏览器页面打开编辑多个Notebook、Ipython console和terminal终端，甚至可以使用Jupyter Lab连接Google Drive等服务。由于Jupyter Lab拥有模块化结构，提供更多类似IDE的体验，已经有越来越多的人从使用Jupyter Notebook转向使用Jupyter Lab。</p>
<p>[* ]通过本章学习，你将收获：</p>
<ul class="simple">
<li><p>安装和配置Jupyter Notebook和Jupyter Lab的方法</p></li>
<li><p>Jupyter Notebook的基本操作和快捷键</p></li>
<li><p>使用Jupyter Notebook编写代码的方式</p></li>
<li><p>了解Jupyter Notebook的Bash命令和魔术命令</p></li>
<li><p>为Jupyter Notebook安装拓展插件的方法</p></li>
</ul>
<section id="id1">
<h2>1 Jupyter Notebook/Lab安装<a class="headerlink" href="#id1" title="永久链接至标题">#</a></h2>
<ol>
<li><p>安装Jupyter Notebook：激活虚拟环境后，我们只需要在终端输入指令</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">conda</span> <span class="n">install</span> <span class="n">jupyter</span> <span class="n">notebook</span>
<span class="c1"># pip install jupyter notebook</span>
</pre></div>
</div>
</li>
</ol>
<ul>
<li><p>注：如果pip版本过低，还需提前运行更新pip的指令</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">pip</span> <span class="n">install</span> <span class="o">--</span><span class="n">upgrade</span> <span class="n">pip</span>
</pre></div>
</div>
</li>
</ul>
<ol>
<li><p>安装Jupyter Lab：激活环境后，我们同样也只需要在终端输入指令</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">conda</span> <span class="n">install</span> <span class="o">-</span><span class="n">c</span> <span class="n">conda</span><span class="o">-</span><span class="n">forge</span> <span class="n">jupyterlab</span>
<span class="c1"># pip install jupyterlab</span>
</pre></div>
</div>
</li>
<li><p>在终端输入指令打开Jupyter Notebook</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">notebook</span>  <span class="c1"># 打开Jupyter Notebook</span>
<span class="n">jupyter</span> <span class="n">lab</span>  <span class="c1"># 打开Jupyter Lab</span>
</pre></div>
</div>
</li>
</ol>
<ul>
<li><p>如果浏览器没有自动打开Jupyter Notebook或者Jupyter Lab，复制端口信息粘贴至浏览器打开</p>
<p><img alt="image-20220812162810305" src="../_images/image-20220812162810305.png" /></p>
</li>
<li><p>如果想要自定义端口，在终端输入如下指令修改</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">notebook</span> <span class="o">--</span><span class="n">port</span> <span class="o">&lt;</span><span class="n">port_number</span><span class="o">&gt;</span>
</pre></div>
</div>
</li>
<li><p>如果想启动服务器但不打开浏览器，可以在终端输入</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">notebook</span> <span class="o">--</span><span class="n">no</span><span class="o">-</span><span class="n">browser</span>
</pre></div>
</div>
</li>
</ul>
</section>
<section id="id2">
<h2>2 Jupyter Notebook/Lab配置<a class="headerlink" href="#id2" title="永久链接至标题">#</a></h2>
<section id="id3">
<h3>2.1 设置文件存放位置<a class="headerlink" href="#id3" title="永久链接至标题">#</a></h3>
<p>​	在使用Jupyter Notebook/Jupyter Lab时，如果我们想要更改默认文件存放路径，该怎么办？</p>
<ul class="simple">
<li><p>Jupyter Notebook</p></li>
</ul>
<ol>
<li><p>我们首先需要查看配置文件，只需要在终端输入</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">notebook</span> <span class="o">--</span><span class="n">generate</span><span class="o">-</span><span class="n">config</span>
</pre></div>
</div>
</li>
<li><p>我们记住出现配置文件的路径，复制到文件夹中打开（终端这里可以写N）</p></li>
</ol>
<img src="figures/image-20220716235510697.png" alt="image-20220716235510697" style="zoom:50%;" />
<ol class="simple">
<li><p>在文件夹中双击打开配置文件</p></li>
</ol>
<img src="figures/image-20220716235540827.png" alt="image-20220716235540827" style="zoom:50%;" />
<ol class="simple">
<li><p>打开Python文件后，用<code class="docutils literal notranslate"><span class="pre">Ctrl+F</span></code>快捷键查找，输入关键词，找到<code class="docutils literal notranslate"><span class="pre">#</span> <span class="pre">c.NotebookApp.notebook_dir</span> <span class="pre">=</span> <span class="pre">''</span></code></p></li>
<li><p>去掉注释，并填充路径<code class="docutils literal notranslate"><span class="pre">c.NotebookApp.notebook_dir</span> <span class="pre">=</span> <span class="pre">'D:\\Adatascience'</span></code></p></li>
</ol>
<img src="figures/image-20220804170455298.png" alt="image-20220804170455298" style="zoom:50%;" />
<ol class="simple">
<li><p>此时我们在终端中输入<code class="docutils literal notranslate"><span class="pre">jupyter</span> <span class="pre">notebook</span></code>，打开页面后发现文件默认路径已经被更改。但是点击菜单栏中的应用快捷方式打开Jupyter Notebook，打开页面发现文件位置仍然是默认路径</p></li>
</ol>
<img src="figures/image-20220716235931573.png" alt="image-20220716235931573" style="zoom:25%;" />
<ol class="simple">
<li><p>如果我们想要更改应用快捷方式Jupyter Notebook的文件位置，此时需要右键选中快捷方式，打开文件所在位置。再右键点击快捷方式，查看属性，再点击快捷方式</p></li>
</ol>
<img src="figures/image-20220717000530228.png" alt="image-20220717000530228" style="zoom:25%;" />
<img src="figures/image-20220804171027983.png" alt="image-20220804171027983" style="zoom: 25%;" />
<ol class="simple">
<li><p>我们只需要在“目标”中删除红框标记部分，点击确定</p></li>
</ol>
<img src="figures/image-20220717001243904.png" alt="image-20220717001243904" style="zoom:50%;" />
<ol class="simple">
<li><p>此时再打开菜单栏中Jupyter Notebook的快捷方式，发现页面文件路径已经变为之前自主设置的路径啦！</p></li>
</ol>
<ul class="simple">
<li><p>Jupyter Lab的修改操作和Jupyter Notebook流程相似，但是在细节上有些不同</p></li>
</ul>
<ol>
<li><p>同样我们还是首先需要查看配置文件，在终端输入</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">lab</span> <span class="o">--</span><span class="n">generate</span><span class="o">-</span><span class="n">config</span>
</pre></div>
</div>
</li>
<li><p>找到配置文件所在的文件夹，打开配置文件</p></li>
<li><p>修改配置文件时，用<code class="docutils literal notranslate"><span class="pre">Ctrl+F</span></code>快捷键查找，输入关键词，找到<code class="docutils literal notranslate"><span class="pre">#</span> <span class="pre">c.ServerApp.notebook_dir</span></code>，去掉注释。改为<code class="docutils literal notranslate"><span class="pre">c.ServerApp.notebook_dir</span> <span class="pre">=</span> <span class="pre">'D:\\Adatascience（这里填自己想改的文件路径）'</span></code></p></li>
<li><p>之后的步骤和Jupyter Notebook修改配置文件的第七至第十步相同</p></li>
</ol>
</section>
<section id="id4">
<h3>2.2 使用虚拟环境<a class="headerlink" href="#id4" title="永久链接至标题">#</a></h3>
<p>​		需要注意的是，Anaconda安装的虚拟环境和Jupyter Notebook运行需要的Kernel并不互通。那么我们该如何解决这个问题，并且如果我们想要切换内核（Change Kernel），该如何操作呢？</p>
<ol class="simple">
<li><p>将在Anaconda中创建的虚拟环境添加<code class="docutils literal notranslate"><span class="pre">ipykernel</span></code></p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># 如果还没创建环境，在创建时要加上ipykernel</span>
<span class="n">conda</span> <span class="n">create</span> <span class="o">-</span><span class="n">n</span> <span class="n">env_name</span> <span class="n">python</span><span class="o">=</span><span class="mf">3.8</span> <span class="n">ipykernel</span>
<span class="c1"># 如果已经创建环境，在环境中安装ipykernel</span>
<span class="n">pip</span> <span class="n">install</span> <span class="n">ipykernel</span>
</pre></div>
</div>
<ol class="simple">
<li><p>将虚拟环境写进Jupyter</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">ipykernel</span> <span class="n">install</span> <span class="o">--</span><span class="n">user</span> <span class="o">--</span><span class="n">name</span> <span class="n">env_name</span> <span class="o">--</span><span class="n">display</span><span class="o">-</span><span class="n">name</span> <span class="s2">&quot;env_name&quot;</span>
</pre></div>
</div>
<ol class="simple">
<li><p>在<code class="docutils literal notranslate"><span class="pre">Kernel</span></code>中更换添加的虚拟环境即可</p></li>
</ol>
<img src="figures/image-20220812171025719.png" alt="image-20220812171025719" style="zoom:25%;" />
</section>
</section>
<section id="id5">
<h2>3 Jupyter Notebook\Lab基本操作<a class="headerlink" href="#id5" title="永久链接至标题">#</a></h2>
<section id="id6">
<h3>3.1 基本使用<a class="headerlink" href="#id6" title="永久链接至标题">#</a></h3>
<section id="jupyter-notebook">
<h4>3.1.1 Jupyter Notebook<a class="headerlink" href="#jupyter-notebook" title="永久链接至标题">#</a></h4>
<ol class="simple">
<li><p>创建文件：点击右上角New，选择Notebook；创建文件夹：点击右上角New，选择Folder</p></li>
</ol>
<img src="figures/image-20220805024106987.png" alt="image-20220805024106987" style="zoom:25%;" />
<ol class="simple">
<li><p>删除文件：点击文件前的方框，再点击删除图标</p></li>
</ol>
<img src="figures/image-20220805030406608.png" alt="image-20220805030406608" style="zoom: 25%;" />
<ol class="simple">
<li><p>重命名文件：当文件在运行时（即文件前图标为绿色），需要先点击“Shutdown”（关闭终端），再点击“Rename”</p></li>
</ol>
<img src="figures/image-20220805031209481.png" alt="image-20220805031209481" style="zoom:25%;" />
<ol class="simple">
<li><p>重命名文件夹：点击文件夹前的方框，再点击“Rename”</p></li>
<li><p>复制（Duplicate）、移动（Move）、下载（Download）、查看（View）等操作，同样可以点击文件前的方框，再点上方的图标进行操作</p></li>
</ol>
</section>
<section id="jupyter-lab">
<h4>3.1.2 Jupyter Lab<a class="headerlink" href="#jupyter-lab" title="永久链接至标题">#</a></h4>
<ol class="simple">
<li><p>红框内按钮从左到右分别是新建文件、新建文件夹、上传文件和刷新</p></li>
</ol>
<p><img alt="image-20220805033034092" src="../_images/image-20220805033034092.png" /></p>
<ol class="simple">
<li><p>上传多个文件的方法：</p></li>
</ol>
<ul class="simple">
<li><p>将文件打包成一个zip压缩包</p></li>
<li><p>上传该压缩包</p></li>
<li><p>解压文件<code class="docutils literal notranslate"><span class="pre">!unzip</span> <span class="pre">(压缩包所在路径)</span> <span class="pre">-d</span> <span class="pre">(解压路径)</span></code>，例如：<code class="docutils literal notranslate"><span class="pre">!unzip</span> <span class="pre">coco.zip</span> <span class="pre">-d</span> <span class="pre">data/coco</span></code></p></li>
<li><p>删除该压缩包</p></li>
</ul>
</section>
</section>
<section id="id7">
<h3>3.2 常用快捷键<a class="headerlink" href="#id7" title="永久链接至标题">#</a></h3>
<ol>
<li><p>入门操作</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># 增加，减少，剪切，保存，删除等</span>
<span class="c1"># a, b, x, s, dd</span>

<span class="c1"># 合并，执行本单元代码，并跳转到下一单元，执行本单元代码，留在本单元</span>
<span class="c1"># Shift+M Shift+Enter Ctrl+Enter</span>

<span class="c1"># 显示行数，切换markdown/code</span>
<span class="c1"># l, m/y</span>
</pre></div>
</div>
</li>
<li><p>Jupyter Notebook中按下Enter进入编辑模式，按下Esc进入命令模式</p></li>
</ol>
<ul>
<li><p><strong>编辑模式（绿色）</strong></p>
<p><img alt="image-20220805144702521" src="../_images/image-20220805144702521.png" /></p>
</li>
<li><p><strong>命令模式（蓝色）</strong></p>
<p><img alt="image-20220805144722375" src="../_images/image-20220805144722375.png" /></p>
</li>
<li><p>在命令模式下，点击h，会弹出快捷键窗口</p></li>
</ul>
<img src="figures/image-20220805144803441.png" alt="image-20220805144803441" style="zoom:25%;" />
<ol class="simple">
<li><p>Jupyter Lab同样有两种模式。按下Enter进入编辑模式，按下Esc进入命令模式</p></li>
</ol>
<ul class="simple">
<li><p>编辑模式（有框线无光标）</p></li>
</ul>
<p><img alt="image-20220805145218241" src="../_images/image-20220805145218241.png" /></p>
<ul class="simple">
<li><p>命令模式（无框线无光标）</p></li>
</ul>
<p><img alt="image-20220805150721875" src="../_images/image-20220805150721875.png" /></p>
<ul class="simple">
<li><p>快捷键操作与Jupyter Notebook基本相同，可参考上一部分</p></li>
</ul>
<ol class="simple">
<li><p>快捷键汇总</p></li>
</ol>
<p>​			命令模式（按<code class="docutils literal notranslate"><span class="pre">Esc</span></code>）</p>
<ul class="simple">
<li><p><strong>Enter</strong> : 转入编辑模式</p></li>
<li><p><strong>Shift-Enter</strong> : 运行本单元，选中下个单元</p></li>
<li><p><strong>Ctrl-Enter</strong> : 运行本单元</p></li>
<li><p><strong>Alt-Enter</strong> : 运行本单元，在其下插入新单元</p></li>
<li><p><strong>Y</strong> : 单元转入代码状态</p></li>
<li><p><strong>M</strong> :单元转入markdown状态</p></li>
<li><p><strong>R</strong> : 单元转入raw状态</p></li>
<li><p><strong>1</strong> : 设定 1 级标题</p></li>
<li><p><strong>2</strong> : 设定 2 级标题</p></li>
<li><p><strong>3</strong> : 设定 3 级标题</p></li>
<li><p><strong>4</strong> : 设定 4 级标题</p></li>
<li><p><strong>5</strong> : 设定 5 级标题</p></li>
<li><p><strong>6</strong> : 设定 6 级标题</p></li>
<li><p><strong>Up</strong> : 选中上方单元</p></li>
<li><p><strong>K</strong> : 选中上方单元</p></li>
<li><p><strong>Down</strong> : 选中下方单元</p></li>
<li><p><strong>J</strong> : 选中下方单元</p></li>
<li><p><strong>Shift-K</strong> : 扩大选中上方单元</p></li>
<li><p><strong>Shift-J</strong> : 扩大选中下方单元</p></li>
<li><p><strong>A</strong> : 在上方插入新单元</p></li>
<li><p><strong>B</strong> : 在下方插入新单元</p></li>
<li><p><strong>X</strong> : 剪切选中的单元</p></li>
<li><p><strong>C</strong> : 复制选中的单元</p></li>
<li><p><strong>Shift-V</strong> : 粘贴到上方单元</p></li>
<li><p><strong>V</strong> : 粘贴到下方单元</p></li>
<li><p><strong>Z</strong> : 恢复删除的最后一个单元</p></li>
<li><p><strong>D,D</strong> : 删除选中的单元</p></li>
<li><p><strong>Shift-M</strong> : 合并选中的单元</p></li>
<li><p><strong>Ctrl-S</strong> : 文件存盘</p></li>
<li><p><strong>S</strong> : 文件存盘</p></li>
<li><p><strong>L</strong> : 转换行号</p></li>
<li><p><strong>O</strong> : 转换输出</p></li>
<li><p><strong>Shift-O</strong> : 转换输出滚动</p></li>
<li><p><strong>Esc</strong> : 关闭页面</p></li>
<li><p><strong>Q</strong> : 关闭页面</p></li>
<li><p><strong>H</strong> : 显示快捷键帮助</p></li>
<li><p><strong>I,I</strong> : 中断Notebook内核</p></li>
<li><p><strong>0,0</strong> : 重启Notebook内核</p></li>
<li><p><strong>Shift</strong> : 忽略</p></li>
<li><p><strong>Shift-Space</strong> : 向上滚动</p></li>
<li><p><strong>Space</strong> : 向下滚动</p></li>
</ul>
<p>​		编辑模式（按<code class="docutils literal notranslate"><span class="pre">Enter</span></code>）</p>
<ul class="simple">
<li><p><strong>Tab</strong> : 代码补全或缩进</p></li>
<li><p><strong>Shift-Tab</strong> : 提示</p></li>
<li><p><strong>Ctrl-]</strong> : 缩进</p></li>
<li><p><strong>Ctrl-[</strong> : 解除缩进</p></li>
<li><p><strong>Ctrl-A</strong> : 全选</p></li>
<li><p><strong>Ctrl-Z</strong> : 复原</p></li>
<li><p><strong>Ctrl-Shift-Z</strong> : 再做</p></li>
<li><p><strong>Ctrl-Y</strong> : 再做</p></li>
<li><p><strong>Ctrl-Home</strong> : 跳到单元开头</p></li>
<li><p><strong>Ctrl-Up</strong> : 跳到单元开头</p></li>
<li><p><strong>Ctrl-End</strong> : 跳到单元末尾</p></li>
<li><p><strong>Ctrl-Down</strong> : 跳到单元末尾</p></li>
<li><p><strong>Ctrl-Left</strong> : 跳到左边一个字首</p></li>
<li><p><strong>Ctrl-Right</strong> : 跳到右边一个字首</p></li>
<li><p><strong>Ctrl-Backspace</strong> : 删除前面一个字</p></li>
<li><p><strong>Ctrl-Delete</strong> : 删除后面一个字</p></li>
<li><p><strong>Esc</strong> : 进入命令模式</p></li>
<li><p><strong>Ctrl-M</strong> : 进入命令模式</p></li>
<li><p><strong>Shift-Enter</strong> : 运行本单元，选中下一单元</p></li>
<li><p><strong>Ctrl-Enter</strong> : 运行本单元</p></li>
<li><p><strong>Alt-Enter</strong> : 运行本单元，在下面插入一单元</p></li>
<li><p><strong>Ctrl-Shift--</strong> : 分割单元</p></li>
<li><p><strong>Ctrl-Shift-Subtract</strong> : 分割单元</p></li>
<li><p><strong>Ctrl-S</strong> : 文件存盘</p></li>
<li><p><strong>Shift</strong> : 忽略</p></li>
<li><p><strong>Up</strong> : 光标上移或转入上一单元</p></li>
<li><p><strong>Down</strong> :光标下移或转入下一单元</p></li>
</ul>
</section>
<section id="id8">
<h3>3.3 安装插件<a class="headerlink" href="#id8" title="永久链接至标题">#</a></h3>
<ul class="simple">
<li><p>Jupyter Notebook安装插件的方法</p></li>
</ul>
<ol>
<li><p>在Anaconda Powershell Prompt中输入</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">pip</span> <span class="n">install</span> <span class="n">jupyter_contrib_nbextensions</span>
</pre></div>
</div>
</li>
<li><p>再次输入以下指令，将插件添加到工具栏</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">contrib</span> <span class="n">nbextension</span> <span class="n">install</span>
</pre></div>
</div>
</li>
<li><p>打开Jupyter Notebook，点击Nbextensions，取消勾选<code class="docutils literal notranslate"><span class="pre">disable</span> <span class="pre">configuration</span> <span class="pre">for</span> <span class="pre">nbextensions</span> <span class="pre">without</span> <span class="pre">explicit</span> <span class="pre">compatibility</span></code>，此时可以添加自己喜欢的插件啦！</p></li>
</ol>
<img src="figures/image-20220805151600140.png" alt="image-20220805151600140" style="zoom:25%;" />
<ol class="simple">
<li><p>推荐以下两个基础插件</p></li>
</ol>
<ul class="simple">
<li><p>Execute Time：可以显示执行一个Cell要花费多少时间</p></li>
<li><p>Hinterland：提供代码补全功能</p></li>
<li><p>Jupyter Lab安装插件的方法</p></li>
</ul>
<ol class="simple">
<li><p>Jupyter Lab安装插件点击左侧的第四个标志，点击“Enable”后就可以在搜索栏中搜索想要的插件</p></li>
</ol>
<img src="figures/image-20220805152331777.png" alt="image-20220805152331777" style="zoom:25%;" />
<ol class="simple">
<li><p>例如搜索<code class="docutils literal notranslate"><span class="pre">jupyterlab-execute-time</span></code>后，在Search Results中查看结果，点击Install便可安装插件</p></li>
</ol>
<img src="figures/image-20220805152507891.png" alt="image-20220805152507891" style="zoom:25%;" />
<ol>
<li><p>还可以在Anaconda Powershell Prompt中使用指令来安装插件</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">jupyter</span> <span class="n">labextension</span> <span class="n">install</span> <span class="n">jupyterlab</span><span class="o">-</span><span class="n">execute</span><span class="o">-</span><span class="n">time</span>  <span class="c1"># 安装jupyterlab-execute-time</span>
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="id9">
<h2>4 进阶操作<a class="headerlink" href="#id9" title="永久链接至标题">#</a></h2>
<p>​		除了以上操作，Jupyter Notebook还有许多丰富的内容等待大家探索。如Bash命令、魔术命令等。我们为大家提供了一份在Notebook中编写的进阶操作，快来试试看吧~</p>
<p>​		<a class="reference external" href="https://github.com/datawhalechina/thorough-pytorch/blob/main/notebook/%E7%AC%AC%E9%9B%B6%E7%AB%A0%20%E5%89%8D%E7%BD%AE%E7%9F%A5%E8%AF%86%E8%A1%A5%E5%85%85/%E8%BF%9B%E9%98%B6%E6%93%8D%E4%BD%9C.ipynb">点击查看进阶教程</a></p>
</section>
<section id="id10">
<h2>5 参考资料<a class="headerlink" href="#id10" title="永久链接至标题">#</a></h2>
<p>【1】<a class="reference external" href="https://zhuanlan.zhihu.com/p/33105153">Jupyter Notebook介绍、安装及使用教程 - 知乎 (zhihu.com)</a></p>
</section>
</section>


              </div>
              
            </main>
            <footer class="footer-article noprint">
                
    <!-- Previous / next buttons -->
<div class='prev-next-area'>
    <a class='left-prev' id="prev-link" href="0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html" title="上一页 页">
        <i class="fas fa-angle-left"></i>
        <div class="prev-next-info">
            <p class="prev-next-subtitle">上一页</p>
            <p class="prev-next-title">常用包的学习</p>
        </div>
    </a>
    <a class='right-next' id="next-link" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/index.html" title="下一页 页">
    <div class="prev-next-info">
        <p class="prev-next-subtitle">下一页</p>
        <p class="prev-next-title">第一章：PyTorch的简介和安装</p>
    </div>
    <i class="fas fa-angle-right"></i>
    </a>
</div>
            </footer>
        </div>
    </div>
    <div class="footer-content row">
        <footer class="col footer"><p>
  
    By ZhikangNiu<br/>
  
      &copy; Copyright 2022, ZhikangNiu.<br/>
</p>
        </footer>
    </div>
    
</div>


      </div>
    </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf"></script>


  </body>
</html>