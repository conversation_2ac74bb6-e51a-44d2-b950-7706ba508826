% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_visualize_system_model.R
\name{visualize_system_model}
\alias{visualize_system_model}
\title{IAN_visualize_system_model.R}
\usage{
visualize_system_model(
  llm_response,
  html_file = "system_model_network.html",
  gene_symbols,
  output_dir
)
}
\arguments{
\item{llm_response}{Character string containing the LLM response with the system network in TSV format.}

\item{html_file}{Character string specifying the name of the HTML file to save the visualization to. Default is "system_model_network.html".}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A `visNetwork` object representing the system model network, or NULL if no valid TSV block is found or the visualization fails.
}
\description{
Extracts the system model network from an LLM response in TSV format and visualizes it using the `visNetwork` package.
}
