% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_enrichment_analysis.R
\name{perform_wp_enrichment}
\alias{perform_wp_enrichment}
\title{Perform WikiPathway Enrichment Analysis}
\usage{
perform_wp_enrichment(
  gene_ids,
  gene_mapping,
  organism,
  pvalue = 0.05,
  output_dir = "enrichment_results"
)
}
\arguments{
\item{gene_ids}{A vector of gene identifiers (ENTREZIDs).}

\item{gene_mapping}{A data frame containing gene mappings between ENTREZID and SYMBOL. Must have columns named "ENTRE<PERSON><PERSON>" and "SYMBOL".}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse".}

\item{pvalue}{Numeric value specifying the p-value threshold for filtering results. Default is 0.05.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A data frame containing the filtered WikiPathway enrichment results, or a list with an "error" element if the analysis fails.
}
\description{
Performs WikiPathway enrichment analysis using the `enrichWP` function from the `clusterProfiler` package.
}
