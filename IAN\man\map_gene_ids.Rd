% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_gene_id_mapper.R
\name{map_gene_ids}
\alias{map_gene_ids}
\title{IAN_gene_id_mapper.R}
\usage{
map_gene_ids(
  input_type,
  markers = NULL,
  deg_file = NULL,
  organism = NULL,
  gene_type = NULL,
  pvalue = 0.05,
  log2FC = 1,
  deseq_results = NULL
)
}
\arguments{
\item{input_type}{Character string specifying the input type. Must be one of "findmarker", "deseq", or "custom".}

\item{markers}{Data frame containing marker genes (required if `input_type` is "findmarker").}

\item{deg_file}{Path to a file containing differentially expressed genes (required if `input_type` is "custom").}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse".}

\item{gene_type}{Character string specifying the gene identifier type in the input data. Must be one of "ENSEMBL", "ENTREZID", or "SYMBOL".}

\item{pvalue}{Numeric value specifying the p-value threshold for filtering marker genes (used if `input_type` is "findmarker"). Default is 0.05.}

\item{log2FC}{Numeric value specifying the log2 fold change threshold for filtering marker genes (used if `input_type` is "findmarker"). Default is 1.}

\item{deseq_results}{Data frame containing DESeq2 results (required if `input_type` is "deseq").}
}
\value{
A data frame with two columns: ENTREZID and SYMBOL, containing the mapped gene identifiers.
}
\description{
Maps gene identifiers from various input types (findmarker, DESeq2, or custom file) to ENTREZID and SYMBOL.
}
