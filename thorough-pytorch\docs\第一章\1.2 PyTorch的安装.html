
<!DOCTYPE html>

<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

    <title>1.2 PyTorch的安装 &#8212; 深入浅出PyTorch</title>
    
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">
<link href="../_static/styles/pydata-sphinx-theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">

    
  <link rel="stylesheet"
    href="../_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" href="../_static/styles/sphinx-book-theme.css?digest=62ba249389abaaa9ffc34bf36a076bdc1d65ee18" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../_static/togglebutton.css" />
    <link rel="stylesheet" type="text/css" href="../_static/mystnb.css" />
    <link rel="stylesheet" type="text/css" href="../_static/plot_directive.css" />
    
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf">

    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/doctools.js"></script>
    <script>let toggleHintShow = 'Click to show';</script>
    <script>let toggleHintHide = 'Click to hide';</script>
    <script>let toggleOpenOnPrint = 'true';</script>
    <script src="../_static/togglebutton.js"></script>
    <script src="../_static/scripts/sphinx-book-theme.js?digest=f31d14ad54b65d19161ba51d4ffff3a77ae00456"></script>
    <script>var togglebuttonSelector = '.toggle, .admonition.dropdown, .tag_hide_input div.cell_input, .tag_hide-input div.cell_input, .tag_hide_output div.cell_output, .tag_hide-output div.cell_output, .tag_hide_cell.cell, .tag_hide-cell.cell';</script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="1.3 PyTorch相关资源" href="1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html" />
    <link rel="prev" title="1.1 PyTorch简介" href="1.1%20PyTorch%E7%AE%80%E4%BB%8B.html" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="docsearch:language" content="zh">
    

    <!-- Google Analytics -->
    
  </head>
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="60">
<!-- Checkboxes to toggle the left sidebar -->
<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation" aria-label="Toggle navigation sidebar">
<label class="overlay overlay-navbar" for="__navigation">
    <div class="visually-hidden">Toggle navigation sidebar</div>
</label>
<!-- Checkboxes to toggle the in-page toc -->
<input type="checkbox" class="sidebar-toggle" name="__page-toc" id="__page-toc" aria-label="Toggle in-page Table of Contents">
<label class="overlay overlay-pagetoc" for="__page-toc">
    <div class="visually-hidden">Toggle in-page Table of Contents</div>
</label>
<!-- Headers at the top -->
<div class="announcement header-item noprint"></div>
<div class="header header-item noprint"></div>

    
    <div class="container-fluid" id="banner"></div>

    

    <div class="container-xl">
      <div class="row">
          
<!-- Sidebar -->
<div class="bd-sidebar noprint" id="site-navigation">
    <div class="bd-sidebar__content">
        <div class="bd-sidebar__top"><div class="navbar-brand-box">
    <a class="navbar-brand text-wrap" href="../index.html">
      
      
      
      <h1 class="site-logo" id="site-title">深入浅出PyTorch</h1>
      
    </a>
</div><form class="bd-search d-flex align-items-center" action="../search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main">
    <div class="bd-toc-item active">
        <p aria-level="2" class="caption" role="heading">
 <span class="caption-text">
  目录
 </span>
</p>
<ul class="current nav bd-sidenav">
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/index.html">
   第零章：前置知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" type="checkbox"/>
  <label for="toctree-checkbox-1">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.1%20%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%AE%80%E5%8F%B2.html">
     人工智能简史
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.2%20%E8%AF%84%E4%BB%B7%E6%8C%87%E6%A0%87.html">
     模型评价指标
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html">
     常用包的学习
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.4%20Jupyter%E7%9B%B8%E5%85%B3%E6%93%8D%E4%BD%9C.html">
     Jupyter notebook/Lab 简述
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 current active has-children">
  <a class="reference internal" href="index.html">
   第一章：PyTorch的简介和安装
  </a>
  <input checked="" class="toctree-checkbox" id="toctree-checkbox-2" name="toctree-checkbox-2" type="checkbox"/>
  <label for="toctree-checkbox-2">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul class="current">
   <li class="toctree-l2">
    <a class="reference internal" href="1.1%20PyTorch%E7%AE%80%E4%BB%8B.html">
     1.1 PyTorch简介
    </a>
   </li>
   <li class="toctree-l2 current active">
    <a class="current reference internal" href="#">
     1.2 PyTorch的安装
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html">
     1.3 PyTorch相关资源
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/index.html">
   第二章：PyTorch基础知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-3" name="toctree-checkbox-3" type="checkbox"/>
  <label for="toctree-checkbox-3">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.1%20%E5%BC%A0%E9%87%8F.html">
     2.1 张量
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.2%20%E8%87%AA%E5%8A%A8%E6%B1%82%E5%AF%BC.html">
     2.2 自动求导
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.3%20%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%E7%AE%80%E4%BB%8B.html">
     2.3 并行计算简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.4%20AI%E7%A1%AC%E4%BB%B6%E5%8A%A0%E9%80%9F%E8%AE%BE%E5%A4%87.html">
     AI硬件加速设备
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/index.html">
   第三章：PyTorch的主要组成模块
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-4" name="toctree-checkbox-4" type="checkbox"/>
  <label for="toctree-checkbox-4">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.1%20%E6%80%9D%E8%80%83%EF%BC%9A%E5%AE%8C%E6%88%90%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E7%9A%84%E5%BF%85%E8%A6%81%E9%83%A8%E5%88%86.html">
     3.1 思考：完成深度学习的必要部分
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.2%20%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE.html">
     3.2 基本配置
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.3%20%E6%95%B0%E6%8D%AE%E8%AF%BB%E5%85%A5.html">
     3.3 数据读入
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.4%20%E6%A8%A1%E5%9E%8B%E6%9E%84%E5%BB%BA.html">
     3.4 模型构建
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.5%20%E6%A8%A1%E5%9E%8B%E5%88%9D%E5%A7%8B%E5%8C%96.html">
     3.5 模型初始化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.6%20%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     3.6 损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.7%20%E8%AE%AD%E7%BB%83%E4%B8%8E%E8%AF%84%E4%BC%B0.html">
     3.7 训练和评估
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.8%20%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     3.8 可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.9%20%E4%BC%98%E5%8C%96%E5%99%A8.html">
     3.9 PyTorch优化器
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/index.html">
   第四章：PyTorch基础实战
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-5" name="toctree-checkbox-5" type="checkbox"/>
  <label for="toctree-checkbox-5">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.1%20ResNet.html">
     4.1 ResNet
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.4%20FashionMNIST%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     基础实战——FashionMNIST时装分类
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/index.html">
   第五章：PyTorch模型定义
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-6" name="toctree-checkbox-6" type="checkbox"/>
  <label for="toctree-checkbox-6">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.1%20PyTorch%E6%A8%A1%E5%9E%8B%E5%AE%9A%E4%B9%89%E7%9A%84%E6%96%B9%E5%BC%8F.html">
     5.1 PyTorch模型定义的方式
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.2%20%E5%88%A9%E7%94%A8%E6%A8%A1%E5%9E%8B%E5%9D%97%E5%BF%AB%E9%80%9F%E6%90%AD%E5%BB%BA%E5%A4%8D%E6%9D%82%E7%BD%91%E7%BB%9C.html">
     5.2 利用模型块快速搭建复杂网络
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.3%20PyTorch%E4%BF%AE%E6%94%B9%E6%A8%A1%E5%9E%8B.html">
     5.3 PyTorch修改模型
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.4%20PyTorh%E6%A8%A1%E5%9E%8B%E4%BF%9D%E5%AD%98%E4%B8%8E%E8%AF%BB%E5%8F%96.html">
     5.4 PyTorch模型保存与读取
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/index.html">
   第六章：PyTorch进阶训练技巧
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-7" name="toctree-checkbox-7" type="checkbox"/>
  <label for="toctree-checkbox-7">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.1%20%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     6.1 自定义损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.2%20%E5%8A%A8%E6%80%81%E8%B0%83%E6%95%B4%E5%AD%A6%E4%B9%A0%E7%8E%87.html">
     6.2 动态调整学习率
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-torchvision.html">
     6.3 模型微调-torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-timm.html">
     6.3 模型微调 - timm
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.4%20%E5%8D%8A%E7%B2%BE%E5%BA%A6%E8%AE%AD%E7%BB%83.html">
     6.4 半精度训练
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.5%20%E6%95%B0%E6%8D%AE%E5%A2%9E%E5%BC%BA-imgaug.html">
     6.5 数据增强-imgaug
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.6%20%E4%BD%BF%E7%94%A8argparse%E8%BF%9B%E8%A1%8C%E8%B0%83%E5%8F%82.html">
     6.6 使用argparse进行调参
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/index.html">
   第七章：PyTorch可视化
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-8" name="toctree-checkbox-8" type="checkbox"/>
  <label for="toctree-checkbox-8">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.1%20%E5%8F%AF%E8%A7%86%E5%8C%96%E7%BD%91%E7%BB%9C%E7%BB%93%E6%9E%84.html">
     7.1 可视化网络结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     7.2 CNN可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.3%20%E4%BD%BF%E7%94%A8TensorBoard%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.3 使用TensorBoard可视化训练过程
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.4%20%E4%BD%BF%E7%94%A8wandb%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.4 使用wandb可视化训练过程
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/index.html">
   第八章：PyTorch生态简介
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-9" name="toctree-checkbox-9" type="checkbox"/>
  <label for="toctree-checkbox-9">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.1%20%E6%9C%AC%E7%AB%A0%E7%AE%80%E4%BB%8B.html">
     8.1 本章简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html">
     8.2 torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.3%20%E8%A7%86%E9%A2%91%20-%20PyTorchVideo.html">
     8.3 PyTorchVideo简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html">
     8.4 torchtext简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.5%20%E9%9F%B3%E9%A2%91%20-%20torchaudio.html">
     8.5 torchaudio简介
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/index.html">
   第九章：PyTorch的模型部署
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-10" name="toctree-checkbox-10" type="checkbox"/>
  <label for="toctree-checkbox-10">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/9.1%20%E4%BD%BF%E7%94%A8ONNX%E8%BF%9B%E8%A1%8C%E9%83%A8%E7%BD%B2%E5%B9%B6%E6%8E%A8%E7%90%86.html">
     9.1 使用ONNX进行部署并推理
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/index.html">
   第十章：常见代码解读
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-11" name="toctree-checkbox-11" type="checkbox"/>
  <label for="toctree-checkbox-11">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.1%20%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     10.1 图像分类简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.2%20%E7%9B%AE%E6%A0%87%E6%A3%80%E6%B5%8B.html">
     目标检测简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.3%20%E5%9B%BE%E5%83%8F%E5%88%86%E5%89%B2.html">
     10.3 图像分割简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ResNet%E6%BA%90%E7%A0%81%E8%A7%A3%E8%AF%BB.html">
     ResNet源码解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/RNN%E8%AF%A6%E8%A7%A3%E5%8F%8A%E5%85%B6%E5%AE%9E%E7%8E%B0.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/LSTM%E8%A7%A3%E8%AF%BB%E5%8F%8A%E5%AE%9E%E6%88%98.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Transformer%20%E8%A7%A3%E8%AF%BB.html">
     Transformer 解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ViT%E8%A7%A3%E8%AF%BB.html">
     ViT解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Swin-Transformer%E8%A7%A3%E8%AF%BB.html">
     Swin Transformer解读
    </a>
   </li>
  </ul>
 </li>
</ul>

    </div>
</nav></div>
        <div class="bd-sidebar__bottom">
             <!-- To handle the deprecated key -->
            
            <div class="navbar_extra_footer">
            Theme by the <a href="https://ebp.jupyterbook.org">Executable Book Project</a>
            </div>
            
        </div>
    </div>
    <div id="rtd-footer-container"></div>
</div>


          


          
<!-- A tiny helper pixel to detect if we've scrolled -->
<div class="sbt-scroll-pixel-helper"></div>
<!-- Main content -->
<div class="col py-0 content-container">
    
    <div class="header-article row sticky-top noprint">
        



<div class="col py-1 d-flex header-article-main">
    <div class="header-article__left">
        
        <label for="__navigation"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="right"
title="Toggle navigation"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-bars"></i>
  </span>

</label>

        
    </div>
    <div class="header-article__right">
<button onclick="toggleFullScreen()"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="bottom"
title="Fullscreen mode"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-expand"></i>
  </span>

</button>

<div class="menu-dropdown menu-dropdown-repository-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Source repositories">
      <i class="fab fa-github"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Source repository"
>
  

<span class="headerbtn__icon-container">
  <i class="fab fa-github"></i>
  </span>
<span class="headerbtn__text-container">repository</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/issues/new?title=Issue%20on%20page%20%2F第一章/1.2 PyTorch的安装.html&body=Your%20issue%20content%20here."
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Open an issue"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-lightbulb"></i>
  </span>
<span class="headerbtn__text-container">open issue</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/edit/master/第一章/1.2 PyTorch的安装.md"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Edit this page"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-pencil-alt"></i>
  </span>
<span class="headerbtn__text-container">suggest edit</span>
</a>

      </li>
      
    </ul>
  </div>
</div>

<div class="menu-dropdown menu-dropdown-download-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Download this page">
      <i class="fas fa-download"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="../_sources/第一章/1.2 PyTorch的安装.md.txt"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Download source file"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file"></i>
  </span>
<span class="headerbtn__text-container">.md</span>
</a>

      </li>
      
      <li>
        
<button onclick="printPdf(this)"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="left"
title="Print to PDF"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file-pdf"></i>
  </span>
<span class="headerbtn__text-container">.pdf</span>
</button>

      </li>
      
    </ul>
  </div>
</div>
<label for="__page-toc"
  class="headerbtn headerbtn-page-toc"
  
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-list"></i>
  </span>

</label>

    </div>
</div>

<!-- Table of contents -->
<div class="col-md-3 bd-toc show noprint">
    <div class="tocsection onthispage pt-5 pb-3">
        <i class="fas fa-list"></i> Contents
    </div>
    <nav id="bd-toc-nav" aria-label="Page">
        <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#anaconda">
   1.2.1 Anaconda的安装
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1-anaconda-miniconda">
     Step 1：安装Anaconda/miniconda
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-2">
     Step 2：检验是否安装成功
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-3">
     Step 3：创建虚拟环境
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id1">
       查看现存虚拟环境
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id2">
       创建虚拟环境
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id3">
       安装包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id4">
       卸载包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id5">
       显示所有安装的包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id6">
       删除虚拟环境命令
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id7">
       激活环境命令
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id8">
       退出当前环境
      </a>
     </li>
    </ul>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-4">
     Step 4：换源
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#pip">
       pip换源
      </a>
      <ul class="nav section-nav flex-column">
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#linux">
         Linux：
        </a>
       </li>
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#windows">
         Windows：
        </a>
       </li>
      </ul>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#conda">
       conda换源（清华源）官方换源帮助
      </a>
      <ul class="nav section-nav flex-column">
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#id9">
         Windows系统：
        </a>
       </li>
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#id10">
         Linux系统：
        </a>
       </li>
      </ul>
     </li>
    </ul>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id11">
   1.2.2 查看显卡
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id12">
     windows：
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id13">
     linux：
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id14">
   1.2.3 安装PyTorch
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1-pytorch">
     Step 1：登录PyTorch官网
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-2-install">
     Step 2：Install
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id15">
     Step 3：选择命令
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id16">
     Step 4：在线下载
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-5">
     Step 5：离线下载
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id17">
       Windows：
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#step-6">
       Step 6：检验是否安装成功
      </a>
     </li>
    </ul>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#pycharm">
   1.2.4 PyCharm安装（可选操作）
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1">
     Step 1：进入官网下载
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id18">
     Step 2：配置环境
    </a>
   </li>
  </ul>
 </li>
</ul>

    </nav>
</div>
    </div>
    <div class="article row">
        <div class="col pl-md-3 pl-lg-5 content-container">
            <!-- Table of contents that is only displayed when printing the page -->
            <div id="jb-print-docs-body" class="onlyprint">
                <h1>1.2 PyTorch的安装</h1>
                <!-- Table of contents -->
                <div id="print-main-content">
                    <div id="jb-print-toc">
                        
                        <div>
                            <h2> Contents </h2>
                        </div>
                        <nav aria-label="Page">
                            <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#anaconda">
   1.2.1 Anaconda的安装
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1-anaconda-miniconda">
     Step 1：安装Anaconda/miniconda
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-2">
     Step 2：检验是否安装成功
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-3">
     Step 3：创建虚拟环境
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id1">
       查看现存虚拟环境
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id2">
       创建虚拟环境
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id3">
       安装包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id4">
       卸载包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id5">
       显示所有安装的包
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id6">
       删除虚拟环境命令
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id7">
       激活环境命令
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id8">
       退出当前环境
      </a>
     </li>
    </ul>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-4">
     Step 4：换源
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#pip">
       pip换源
      </a>
      <ul class="nav section-nav flex-column">
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#linux">
         Linux：
        </a>
       </li>
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#windows">
         Windows：
        </a>
       </li>
      </ul>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#conda">
       conda换源（清华源）官方换源帮助
      </a>
      <ul class="nav section-nav flex-column">
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#id9">
         Windows系统：
        </a>
       </li>
       <li class="toc-h5 nav-item toc-entry">
        <a class="reference internal nav-link" href="#id10">
         Linux系统：
        </a>
       </li>
      </ul>
     </li>
    </ul>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id11">
   1.2.2 查看显卡
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id12">
     windows：
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id13">
     linux：
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id14">
   1.2.3 安装PyTorch
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1-pytorch">
     Step 1：登录PyTorch官网
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-2-install">
     Step 2：Install
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id15">
     Step 3：选择命令
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id16">
     Step 4：在线下载
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-5">
     Step 5：离线下载
    </a>
    <ul class="nav section-nav flex-column">
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#id17">
       Windows：
      </a>
     </li>
     <li class="toc-h4 nav-item toc-entry">
      <a class="reference internal nav-link" href="#step-6">
       Step 6：检验是否安装成功
      </a>
     </li>
    </ul>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#pycharm">
   1.2.4 PyCharm安装（可选操作）
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#step-1">
     Step 1：进入官网下载
    </a>
   </li>
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#id18">
     Step 2：配置环境
    </a>
   </li>
  </ul>
 </li>
</ul>

                        </nav>
                    </div>
                </div>
            </div>
            <main id="main-content" role="main">
                
              <div>
                
  <section class="tex2jax_ignore mathjax_ignore" id="pytorch">
<h1>1.2 PyTorch的安装<a class="headerlink" href="#pytorch" title="永久链接至标题">#</a></h1>
<p>PyTorch的安装是我们学习PyTorch的第一步，也是经常出错的一步。在安装PyTorch时，我们通常使用的是<strong>Anaconda/miniconda+Pytorch</strong>+ IDE 的流程。</p>
<p>经过本节的学习，你将收获：</p>
<ul class="simple">
<li><p>Anaconda/miniconda的安装及其常见命令</p></li>
<li><p>PyTorch的安装流程</p></li>
<li><p>如何选择一个适合自己的PyTorch版本</p></li>
</ul>
<section id="anaconda">
<h2>1.2.1 Anaconda的安装<a class="headerlink" href="#anaconda" title="永久链接至标题">#</a></h2>
<p>在数据科学和最近很火的深度学习中，要用到大量成熟的package。我们一个个安装 package 很麻烦，而且很容易出现包之间的依赖不适配的问题。而 Anaconda/miniconda的出现很好的解决了我们的问题，它集成了常用于科学分析（机器学习， 深度学习）的大量package，并且借助于conda我们可以实现对虚拟Python环境的管理。</p>
<section id="step-1-anaconda-miniconda">
<h3>Step 1：安装Anaconda/miniconda<a class="headerlink" href="#step-1-anaconda-miniconda" title="永久链接至标题">#</a></h3>
<p>登陆<a class="reference external" href="https://www.anaconda.com/products/individual">Anaconda | Individual Edition</a>，选择相应系统DownLoad，此处以Windows为例（Linux可以点击<a class="reference external" href="https://docs.conda.io/en/latest/miniconda.html">链接</a>选择合适的版本进行下载或者通过官方提供的shell脚本进行下载）：</p>
<p><img alt="anaconda" src="../_images/Anaconda.png" /></p>
</section>
<section id="step-2">
<h3>Step 2：检验是否安装成功<a class="headerlink" href="#step-2" title="永久链接至标题">#</a></h3>
<p>在开始页找到Anaconda Prompt，一般在Anaconda3的文件夹下,( Linux在终端下就行了）</p>
<p><img alt="prompt" src="../_images/prompt.png" /></p>
</section>
<section id="step-3">
<h3>Step 3：创建虚拟环境<a class="headerlink" href="#step-3" title="永久链接至标题">#</a></h3>
<p>Linux在终端(<code class="docutils literal notranslate"><span class="pre">Ctrl</span></code>+<code class="docutils literal notranslate"><span class="pre">Alt</span></code>+<code class="docutils literal notranslate"><span class="pre">T</span></code>)进行，Windows在<code class="docutils literal notranslate"><span class="pre">Anaconda</span> <span class="pre">Prompt</span></code>进行</p>
<section id="id1">
<h4>查看现存虚拟环境<a class="headerlink" href="#id1" title="永久链接至标题">#</a></h4>
<p>查看已经安装好的虚拟环境，可以看到我们这里已经有两个环境存在了</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda env list 
</pre></div>
</div>
<p><img alt="env_list" src="../_images/env_list.png" /></p>
</section>
<section id="id2">
<h4>创建虚拟环境<a class="headerlink" href="#id2" title="永久链接至标题">#</a></h4>
<p>在深度学习和机器学习中，我们经常会创建不同版本的虚拟环境来满足我们的一些需求。下面我们介绍创建虚拟环境的命令。</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda create -n env_name <span class="nv">python</span><span class="o">==</span>version 
<span class="c1"># 注：将env_name 替换成你的环境的名称，version替换成对应的版本号，eg：3.8</span>
</pre></div>
</div>
<p><img alt="" src="../_images/install.png" /></p>
<p><strong>注</strong>：</p>
<ol class="simple">
<li><p>这里忽略我们的warning，因为我们测试的时候已经安了又卸载一遍了，正常时是不会有warning的。</p></li>
<li><p>在选择Python版本时，不要选择太高，建议选择3.6-3.8，版本过高会导致相关库不适配。</p></li>
</ol>
</section>
<section id="id3">
<h4>安装包<a class="headerlink" href="#id3" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda install package_name 
<span class="c1"># 注：package_name 替换成对应的包的名称，eg: pandas</span>
</pre></div>
</div>
</section>
<section id="id4">
<h4>卸载包<a class="headerlink" href="#id4" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda remove package_name
<span class="c1"># 注：package_name 替换成对应的包的名称，eg: pandas</span>
</pre></div>
</div>
</section>
<section id="id5">
<h4>显示所有安装的包<a class="headerlink" href="#id5" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda list
</pre></div>
</div>
</section>
<section id="id6">
<h4>删除虚拟环境命令<a class="headerlink" href="#id6" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda remove -n env_name --all 
<span class="c1"># 注：env_name 替换成对应的环境的名称</span>
</pre></div>
</div>
</section>
<section id="id7">
<h4>激活环境命令<a class="headerlink" href="#id7" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda activate env_name
<span class="c1"># 注：env_name 替换成对应的环境的名称</span>
</pre></div>
</div>
</section>
<section id="id8">
<h4>退出当前环境<a class="headerlink" href="#id8" title="永久链接至标题">#</a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda deactivate
</pre></div>
</div>
<p>关于更多的命令，我们可以查看Anaconda/miniconda官方提供的命令，官网链接：<a class="reference external" href="https://docs.conda.io/projects/conda/en/latest/commands.html#conda-general-commands">点击这里</a></p>
</section>
</section>
<section id="step-4">
<h3>Step 4：换源<a class="headerlink" href="#step-4" title="永久链接至标题">#</a></h3>
<p>在安装package时，我们经常会使用<code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">package_name</span></code>和<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">install</span> <span class="pre">package_name</span></code> 的命令，但是一些package下载速度会很慢，因此我们需要进行换源，换成国内源，加快我们的下载速度。以下便是两种对应方式的永久换源。如果我们仅仅想为单次下载换源可以使用<code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">package_name</span> <span class="pre">-i</span> <span class="pre">https://pypi.tuna.tsinghua.edu.cn/simple</span></code>进行下载。</p>
<section id="pip">
<h4>pip换源<a class="headerlink" href="#pip" title="永久链接至标题">#</a></h4>
<section id="linux">
<h5>Linux：<a class="headerlink" href="#linux" title="永久链接至标题">#</a></h5>
<p>Linux下的换源，我们首先需要在用户目录下新建文件夹<code class="docutils literal notranslate"><span class="pre">.pip</span></code>，并且在文件夹内新建文件<code class="docutils literal notranslate"><span class="pre">pip.conf</span></code>，具体命令如下</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span> ~
mkdir .pip/
vi pip.conf
</pre></div>
</div>
<p>随后，我们需要在<code class="docutils literal notranslate"><span class="pre">pip.conf</span></code>添加下方的内容:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="o">[</span>global<span class="o">]</span>
index-url <span class="o">=</span> http://pypi.douban.com/simple
<span class="o">[</span>install<span class="o">]</span>
use-mirrors <span class="o">=</span><span class="nb">true</span>
<span class="nv">mirrors</span> <span class="o">=</span>http://pypi.douban.com/simple/
trusted-host <span class="o">=</span>pypi.douban.com
</pre></div>
</div>
</section>
<section id="windows">
<h5>Windows：<a class="headerlink" href="#windows" title="永久链接至标题">#</a></h5>
<p>1、文件管理器文件路径地址栏敲：<code class="docutils literal notranslate"><span class="pre">%APPDATA%</span></code> 回车，快速进入 <code class="docutils literal notranslate"><span class="pre">C:\Users\<USER>\AppData\Roaming</span></code> 文件夹中
2、新建 pip 文件夹并在文件夹中新建 <code class="docutils literal notranslate"><span class="pre">pip.ini</span></code> 配置文件
3、我们需要在<code class="docutils literal notranslate"><span class="pre">pip.ini</span></code> 配置文件内容，我们可以选择使用记事本打开，输入以下内容，并按下ctrl+s保存，在这里我们使用的是豆瓣源为例子。</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="o">[</span>global<span class="o">]</span>
index-url <span class="o">=</span> http://pypi.douban.com/simple
<span class="o">[</span>install<span class="o">]</span>
use-mirrors <span class="o">=</span><span class="nb">true</span>
<span class="nv">mirrors</span> <span class="o">=</span>http://pypi.douban.com/simple/
trusted-host <span class="o">=</span>pypi.douban.com
</pre></div>
</div>
</section>
</section>
<section id="conda">
<h4>conda换源（清华源）<a class="reference external" href="https://mirrors.tuna.tsinghua.edu.cn/help/anaconda/">官方换源帮助</a><a class="headerlink" href="#conda" title="永久链接至标题">#</a></h4>
<section id="id9">
<h5>Windows系统：<a class="headerlink" href="#id9" title="永久链接至标题">#</a></h5>
<p>TUNA 提供了 Anaconda 仓库与第三方源的镜像，各系统都可以通过修改用户目录下的 <code class="docutils literal notranslate"><span class="pre">.condarc</span></code> 文件。Windows 用户无法直接创建名为 <code class="docutils literal notranslate"><span class="pre">.condarc</span></code> 的文件，可先执行<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">config</span> <span class="pre">--set</span> <span class="pre">show_channel_urls</span> <span class="pre">yes</span></code>生成该文件之后再修改。</p>
<p>完成这一步后，我们需要修改<code class="docutils literal notranslate"><span class="pre">C:\Users\<USER>\.condarc</span></code>这个文件，打开后将文件里原始内容删除，将下面的内容复制进去并保存。</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>channels:
  - defaults
show_channel_urls: <span class="nb">true</span>
default_channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2
custom_channels:
  conda-forge: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  msys2: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  bioconda: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  menpo: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  pytorch: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  simpleitk: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
</pre></div>
</div>
<p>这一步完成后，我们需要打开<code class="docutils literal notranslate"><span class="pre">Anaconda</span> <span class="pre">Prompt</span></code> 运行 <code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">clean</span> <span class="pre">-i</span></code> 清除索引缓存，保证用的是镜像站提供的索引。</p>
</section>
<section id="id10">
<h5>Linux系统：<a class="headerlink" href="#id10" title="永久链接至标题">#</a></h5>
<p>在Linux系统下，我们还是需要修改<code class="docutils literal notranslate"><span class="pre">.condarc</span></code>来进行换源</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span> ~
vi .condarc
</pre></div>
</div>
<p>在<code class="docutils literal notranslate"><span class="pre">vim</span></code>下，我们需要输入<code class="docutils literal notranslate"><span class="pre">i</span></code>进入编辑模式，将上方内容粘贴进去，按<code class="docutils literal notranslate"><span class="pre">ESC</span></code>退出编辑模式，输入<code class="docutils literal notranslate"><span class="pre">:wq</span></code>保存并退出</p>
<p><img alt="换源内容" src="../_images/Linux_source.png" /></p>
<p>我们可以通过<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">config</span> <span class="pre">--show</span> <span class="pre">default_channels</span></code>检查下是否换源成功，如果出现下图内容，即代表我们换源成功。</p>
<p><img alt="" src="../_images/source_ok.png" /></p>
<p>同时，我们仍然需要<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">clean</span> <span class="pre">-i</span></code> 清除索引缓存，保证用的是镜像站提供的索引。</p>
</section>
</section>
</section>
</section>
<section id="id11">
<h2>1.2.2 查看显卡<a class="headerlink" href="#id11" title="永久链接至标题">#</a></h2>
<p>该部分如果仅仅只有CPU或者集显的小伙伴们可以跳过该部分</p>
<section id="id12">
<h3>windows：<a class="headerlink" href="#id12" title="永久链接至标题">#</a></h3>
<p>我们可以通过在<code class="docutils literal notranslate"><span class="pre">cmd/terminal中</span></code>输入<code class="docutils literal notranslate"><span class="pre">nvidia-smi</span></code>（Linux和Win命令一样）、使用NVIDIA控制面板和使用任务管理器查看自己是否有NVIDIA的独立显卡及其型号</p>
<p><img alt="查看GPU" src="../_images/gpu.png" /></p>
<p><img alt="查看显卡" src="../_images/Nvidia.png" /></p>
</section>
<section id="id13">
<h3>linux：<a class="headerlink" href="#id13" title="永久链接至标题">#</a></h3>
<p><img alt="Linux查看GPU" src="../_images/Linux_GPU.png" /></p>
<p>我们需要看下版本号，看自己可以兼容的CUDA版本，等会安装PyTorch时是可以向下兼容的。具体适配表如下图所示。</p>
<p><img alt="适配表" src="../_images/table.png" /></p>
</section>
</section>
<section id="id14">
<h2>1.2.3 安装PyTorch<a class="headerlink" href="#id14" title="永久链接至标题">#</a></h2>
<section id="step-1-pytorch">
<h3>Step 1：登录<a class="reference external" href="https://pytorch.org/">PyTorch官网</a><a class="headerlink" href="#step-1-pytorch" title="永久链接至标题">#</a></h3>
<p><img alt="" src="../_images/Pytorch.png" /></p>
</section>
<section id="step-2-install">
<h3>Step 2：Install<a class="headerlink" href="#step-2-install" title="永久链接至标题">#</a></h3>
<p><img alt="" src="../_images/download.png" /></p>
<p>这个界面我们可以选择本地开始（Start Locally），云开发（Cloud Partners)，以前的Pytorch版本（Previous PyTorch Versions），移动端开发（Mobile），在此处我们需要进行本地安装。</p>
</section>
<section id="id15">
<h3>Step 3：选择命令<a class="headerlink" href="#id15" title="永久链接至标题">#</a></h3>
<p>我们需要结合自己情况选择命令并复制下来，然后使用conda下载或者pip下载（建议conda安装）</p>
<p>打开<code class="docutils literal notranslate"><span class="pre">Terminal</span></code>，输入<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">activate</span> <span class="pre">env_name</span></code>(env_name 为你对应的环境名称)，切换到对应的环境下面，我们就可以进行PyTorch的安装了。</p>
<p><img alt="" src="../_images/choose_envs.png" /></p>
<p><strong>注</strong>：</p>
<ol class="simple">
<li><p><strong>Stable</strong>代表的是稳定版本，<strong>Preview</strong>代表的是先行版本</p></li>
<li><p>可以结合电脑是否有显卡，选择CPU版本还是CUDA版本，CUDA版本需要拥有独显且是NVIDIA的GPU</p></li>
<li><p>官方建议我们使用<strong>Anaconda/miniconda</strong>来进行管理</p></li>
<li><p>关于安装的系统要求</p>
<ol class="simple">
<li><p><strong>Windows</strong>：</p>
<ol class="simple">
<li><p>Windows 7及更高版本；建议使用Windows 10或者更高的版本</p></li>
<li><p>Windows Server 2008 r2 及更高版本</p></li>
</ol>
</li>
<li><p><strong>Linux：以常见的CentOS和Ubuntu为例</strong></p>
<ol class="simple">
<li><p>CentOS, 最低版本7.3-1611</p></li>
<li><p>Ubuntu, 最低版本 13.04，这里会导致cuda安装的最大版本不同</p></li>
</ol>
</li>
<li><p><strong>macOS</strong>：</p>
<ol class="simple">
<li><p>macOS 10.10及其以上</p></li>
</ol>
</li>
</ol>
</li>
<li><p>有些电脑所支持的cuda版本&lt;10.2，此时我们需要进行手动降级，即就是cudatoolkit = 你所适合的版本，但是这里需要注意下一定要保持PyTorch和cudatoolkit的版本适配。查看<a class="reference external" href="https://pytorch.org/get-started/previous-versions/">Previous PyTorch Versions | PyTorch</a></p></li>
</ol>
</section>
<section id="id16">
<h3>Step 4：在线下载<a class="headerlink" href="#id16" title="永久链接至标题">#</a></h3>
<p>如果我们使用的<code class="docutils literal notranslate"><span class="pre">Anaconda</span> <span class="pre">Prompt</span></code>进行下载的话，我们需要先通过<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">activate</span> <span class="pre">env_name</span></code>，激活我们的虚拟环境中去，再输入命令。</p>
<p><strong>注</strong>: 我们需要要把下载指令后面的 -c pytorch 去掉以保证使用清华源下载，否则还是默认从官网下载。</p>
</section>
<section id="step-5">
<h3>Step 5：离线下载<a class="headerlink" href="#step-5" title="永久链接至标题">#</a></h3>
<section id="id17">
<h4>Windows：<a class="headerlink" href="#id17" title="永久链接至标题">#</a></h4>
<p>在安装的过程中，我们可能会出现一些奇奇怪怪的问题，导致在线下载不成功，我们也可以使用<strong>离线下载</strong>的方法进行。</p>
<p><strong>下载地址</strong>：https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/</p>
<p>通过上面下载地址，我们需要下载好对应版本的pytorch和 torchvision 包，然后打开<code class="docutils literal notranslate"><span class="pre">Anaconda</span> <span class="pre">Prompt</span></code>/<code class="docutils literal notranslate"><span class="pre">Terminal</span></code>中，进入我们安装的路径下。</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span> package_location
conda activate env_name
</pre></div>
</div>
<p>接下来输入以下命令安装两个包</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>conda install --offline pytorch压缩包的全称（后缀都不能忘记）
conda install --offline torchvision压缩包的全称（后缀都不能忘记）
</pre></div>
</div>
</section>
<section id="step-6">
<h4>Step 6：检验是否安装成功<a class="headerlink" href="#step-6" title="永久链接至标题">#</a></h4>
<p>进入所在的<strong>虚拟环境</strong>，紧接着输入<code class="docutils literal notranslate"><span class="pre">python</span></code>，在输入下面的代码。</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch</span>

<span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span>
</pre></div>
</div>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>False
</pre></div>
</div>
<p>这条命令意思是检验是否可以调用cuda，如果我们<strong>安装的是CPU版本的话会返回False，能够调用GPU的会返回True</strong>。一般这个命令不报错的话就证明安装成功。</p>
<ul class="simple">
<li><p>Windows系统</p></li>
</ul>
<p><img alt="验证安装" src="../_images/check_windows.png" /></p>
<ul class="simple">
<li><p>Linux系统</p></li>
</ul>
<p><img alt="" src="../_images/check_linux.png" /></p>
<p>PyTorch的安装绝对是一个容易上火的过程，而且网络上的教程很可能对应早期的版本，或是会出现一些奇奇怪怪的问题，但是别担心，多装几次多遇到点奇奇怪怪的问题就好了！</p>
</section>
</section>
</section>
<section id="pycharm">
<h2>1.2.4 PyCharm安装（可选操作）<a class="headerlink" href="#pycharm" title="永久链接至标题">#</a></h2>
<p>VSCode这些也是ok的，安装PyCharm非必须操作</p>
<p>Linux，Windows此处操作相同，我们建议Windows的同学安装Pycharm即可，因为在Linux上pycharm并不是主流的IDE。</p>
<section id="step-1">
<h3>Step 1：进入<a class="reference external" href="https://www.jetbrains.com/pycharm/">官网</a>下载<a class="headerlink" href="#step-1" title="永久链接至标题">#</a></h3>
<p>如果是学生的话可以使用学生邮箱注册并下载Professional版本，Community版本也基本能满足我们的日常需求。</p>
<p><img alt="Pycharm安装" src="../_images/Pycharm.png" /></p>
</section>
<section id="id18">
<h3>Step 2：配置环境<a class="headerlink" href="#id18" title="永久链接至标题">#</a></h3>
<p>我们需要将虚拟环境设为我们的编译器，具体操作：File --&gt; Settings --&gt; Project:你的项目名称--&gt; Python Interpreter</p>
<p>进去后，我们可以看见他使用的是默认的base环境，现在我们需要将这个环境设置成我们的<code class="docutils literal notranslate"><span class="pre">test</span></code>环境,点击<code class="docutils literal notranslate"><span class="pre">齿轮</span></code>，选择<code class="docutils literal notranslate"><span class="pre">Add</span></code></p>
<p><img alt="改环境" src="../_images/envs1.png" /></p>
<p>点击<code class="docutils literal notranslate"><span class="pre">Conda</span> <span class="pre">Environment</span></code> ，选择<code class="docutils literal notranslate"><span class="pre">Existing</span> <span class="pre">environment</span></code>，将<code class="docutils literal notranslate"><span class="pre">Interpreter</span></code>设置为test环境下的<code class="docutils literal notranslate"><span class="pre">python.exe</span></code></p>
<p><img alt="改环境" src="../_images/envs2.png" /></p>
<p>注：如果在pycharm的环境时，想进入我们的虚拟环境，要使用<code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">activate</span> <span class="pre">名称</span></code></p>
</section>
</section>
</section>


              </div>
              
            </main>
            <footer class="footer-article noprint">
                
    <!-- Previous / next buttons -->
<div class='prev-next-area'>
    <a class='left-prev' id="prev-link" href="1.1%20PyTorch%E7%AE%80%E4%BB%8B.html" title="上一页 页">
        <i class="fas fa-angle-left"></i>
        <div class="prev-next-info">
            <p class="prev-next-subtitle">上一页</p>
            <p class="prev-next-title">1.1 PyTorch简介</p>
        </div>
    </a>
    <a class='right-next' id="next-link" href="1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html" title="下一页 页">
    <div class="prev-next-info">
        <p class="prev-next-subtitle">下一页</p>
        <p class="prev-next-title">1.3 PyTorch相关资源</p>
    </div>
    <i class="fas fa-angle-right"></i>
    </a>
</div>
            </footer>
        </div>
    </div>
    <div class="footer-content row">
        <footer class="col footer"><p>
  
    By ZhikangNiu<br/>
  
      &copy; Copyright 2022, ZhikangNiu.<br/>
</p>
        </footer>
    </div>
    
</div>


      </div>
    </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf"></script>


  </body>
</html>