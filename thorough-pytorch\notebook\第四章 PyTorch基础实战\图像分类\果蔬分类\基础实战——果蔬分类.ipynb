{"cells": [{"cell_type": "code", "execution_count": 212, "id": "594e6ae1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["archive.zip  fruit_vegetable_cls.ipynb\ttest  train  validation\r\n"]}], "source": ["!ls"]}, {"cell_type": "markdown", "id": "0404e977", "metadata": {}, "source": ["数据集地址：[kaggle 果蔬分类数据集](https://www.kaggle.com/kritikseth/fruit-and-vegetable-image-recognition)\n", "\n", "百度网盘：链接：https://pan.baidu.com/s/1q-fS2M97er1-769Ol-htSg 提取码：t0ww"]}, {"cell_type": "code", "execution_count": 213, "id": "f72209d2", "metadata": {}, "outputs": [], "source": ["# 导入必要的包\n", "import os\n", "import torch\n", "import timm\n", "from torchvision import transforms\n", "import numpy as np\n", "import torch.nn as nn\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 214, "id": "5d037dcc", "metadata": {}, "outputs": [], "source": ["# 设置随机种子，确保结果可以复现\n", "def set_seed(seed):\n", "    torch.manual_seed(seed)\n", "    torch.cuda.manual_seed_all(seed)\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.backends.cudnn.benchmark = False\n", "    torch.backends.cudnn.deterministic = True"]}, {"cell_type": "code", "execution_count": 210, "id": "0b185b8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Categories: 36\n"]}], "source": ["# 设置文件路径\n", "train_path = './train/'\n", "val_path = './validation/'\n", "test_path = './test/'\n", "\n", "# 设置超参数\n", "device = torch.device(\"cuda:1\" if torch.cuda.is_available() else \"cpu\")\n", "batch_size = 32\n", "\n", "# num_workers wins用户只能指定为0\n", "num_workers = 4\n", "lr = 3e-4\n", "epochs = 10\n", "\n", "# 输出类别\n", "categories = os.listdir(train_path)\n", "print(f\"Categories: {len(categories)}\")"]}, {"cell_type": "code", "execution_count": 216, "id": "712755de", "metadata": {}, "outputs": [], "source": ["# 使用imgaug对图像进行数据增强\n", "import imageio\n", "import imgaug as ia\n", "from imgaug import augmenters as iaa\n", "\n", "class ImgAugTransform:\n", "    def __init__(self):\n", "        self.aug = iaa.Sequential([\n", "            iaa.Scale((224, 224)),\n", "            iaa.Sometimes(0.25, iaa.<PERSON><PERSON>(sigma=(0, 3.0))),\n", "            <PERSON><PERSON><PERSON>(0.5),\n", "            iaa.Affine(rotate=(-20, 20), mode='symmetric'),\n", "            iaa.Sometimes(0.25,\n", "                          iaa.OneOf([iaa.Dropout(p=(0, 0.1)),\n", "                                     iaa.CoarseDropout(0.1, size_percent=0.5)])),\n", "            iaa.AddToHueAndSaturation(value=(-10, 10), per_channel=True)\n", "        ])\n", "\n", "    def __call__(self, img):\n", "        img = np.array(img)\n", "        return self.aug.augment_image(img).transpose(2,1,0)\n", "\n", "# linux用户num_worker≠0时使用\n", "def worker_init_fn(worker_id):\n", "    imgaug.seed(np.random.get_state()[1][0] + worker_id)\n", "\n", "tfs = ImgAugTransform()"]}, {"cell_type": "code", "execution_count": 220, "id": "3060c38b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 3, 224, 224])\n", "<class 'torch.Tensor'>\n", "torch.<PERSON><PERSON>([3, 224, 224])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from torchvision.datasets import ImageFolder\n", "from torch.utils.data import DataLoader\n", "from PIL import Image\n", "# 自定义数据集\n", "train_dataset = ImageFolder(train_path,transform=tfs)\n", "val_dataset = ImageFolder(val_path,transform=tfs)\n", "test_dataset = ImageFolder(test_path,transform=tfs)\n", "\n", "train_loader = DataLoader(train_dataset,batch_size=batch_size,worker_init_fn=worker_init_fn,shuffle=True)\n", "val_loader = DataLoader(val_dataset,batch_size=batch_size,worker_init_fn=worker_init_fn,shuffle=False)\n", "test_loader = DataLoader(test_dataset,batch_size=batch_size,worker_init_fn=worker_init_fn,shuffle=False)\n", "\n", "# 可视化图片\n", "images, labels = next(iter(train_loader))\n", "print(images.shape)\n", "print(type(images))\n", "print(images[1].shape)\n", "plt.imshow(images[0].permute(1,2,0))\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 222, "id": "a2c608c9", "metadata": {}, "outputs": [], "source": ["import timm\n", "# 使用timm使用预训练模型\n", "model = timm.create_model(\"resnet18\",num_classes=36,pretrained=True).to(device)"]}, {"cell_type": "code", "execution_count": 223, "id": "9171f018", "metadata": {}, "outputs": [{"data": {"text/plain": ["592"]}, "execution_count": 223, "metadata": {}, "output_type": "execute_result"}], "source": ["avail_pretrained_models = timm.list_models(pretrained=True)\n", "len(avail_pretrained_models)"]}, {"cell_type": "code", "execution_count": 227, "id": "fee53ac8", "metadata": {}, "outputs": [{"data": {"text/plain": ["['cspresnet50',\n", " 'cspresnet50d',\n", " 'cspresnet50w',\n", " 'eca_resnet33ts',\n", " 'ecaresnet26t',\n", " 'ecaresnet50d',\n", " 'ecaresnet50d_pruned',\n", " 'ecaresnet50t',\n", " 'ecaresnet101d',\n", " 'ecaresnet101d_pruned',\n", " 'ecaresnet200d',\n", " 'ecaresnet269d',\n", " 'ecaresnetlight',\n", " 'ens_adv_inception_resnet_v2',\n", " 'gcresnet33ts',\n", " 'gcresnet50t',\n", " 'gluon_resnet18_v1b',\n", " 'gluon_resnet34_v1b',\n", " 'gluon_resnet50_v1b',\n", " 'gluon_resnet50_v1c',\n", " 'gluon_resnet50_v1d',\n", " 'gluon_resnet50_v1s',\n", " 'gluon_resnet101_v1b',\n", " 'gluon_resnet101_v1c',\n", " 'gluon_resnet101_v1d',\n", " 'gluon_resnet101_v1s',\n", " 'gluon_resnet152_v1b',\n", " 'gluon_resnet152_v1c',\n", " 'gluon_resnet152_v1d',\n", " 'gluon_resnet152_v1s',\n", " 'inception_resnet_v2',\n", " 'lambda_resnet26rpt_256',\n", " 'lambda_resnet26t',\n", " 'lambda_resnet50ts',\n", " 'legacy_seresnet18',\n", " 'legacy_seresnet34',\n", " 'legacy_seresnet50',\n", " 'legacy_seresnet101',\n", " 'legacy_seresnet152',\n", " 'nf_ecaresnet26',\n", " 'nf_ecaresnet50',\n", " 'nf_ecaresnet101',\n", " 'nf_resnet26',\n", " 'nf_resnet50',\n", " 'nf_resnet101',\n", " 'nf_seresnet26',\n", " 'nf_seresnet50',\n", " 'nf_seresnet101',\n", " 'resnet18',\n", " 'resnet18d',\n", " 'resnet26',\n", " 'resnet26d',\n", " 'resnet26t',\n", " 'resnet32ts',\n", " 'resnet33ts',\n", " 'resnet34',\n", " 'resnet34d',\n", " 'resnet50',\n", " 'resnet50_gn',\n", " 'resnet50d',\n", " 'resnet50t',\n", " 'resnet51q',\n", " 'resnet61q',\n", " 'resnet101',\n", " 'resnet101d',\n", " 'resnet152',\n", " 'resnet152d',\n", " 'resnet200',\n", " 'resnet200d',\n", " 'resnetblur18',\n", " 'resnetblur50',\n", " 'resnetrs50',\n", " 'resnetrs101',\n", " 'resnetrs152',\n", " 'resnetrs200',\n", " 'resnetrs270',\n", " 'resnetrs350',\n", " 'resnetrs420',\n", " 'resnetv2_50',\n", " 'resnetv2_50d',\n", " 'resnetv2_50d_evob',\n", " 'resnetv2_50d_evos',\n", " 'resnetv2_50d_gn',\n", " 'resnetv2_50t',\n", " 'resnetv2_50x1_bit_distilled',\n", " 'resnetv2_50x1_bitm',\n", " 'resnetv2_50x1_bitm_in21k',\n", " 'resnetv2_50x3_bitm',\n", " 'resnetv2_50x3_bitm_in21k',\n", " 'resnetv2_101',\n", " 'resnetv2_101d',\n", " 'resnetv2_101x1_bitm',\n", " 'resnetv2_101x1_bitm_in21k',\n", " 'resnetv2_101x3_bitm',\n", " 'resnetv2_101x3_bitm_in21k',\n", " 'resnetv2_152',\n", " 'resnetv2_152d',\n", " 'resnetv2_152x2_bit_teacher',\n", " 'resnetv2_152x2_bit_teacher_384',\n", " 'resnetv2_152x2_bitm',\n", " 'resnetv2_152x2_bitm_in21k',\n", " 'resnetv2_152x4_bitm',\n", " 'resnetv2_152x4_bitm_in21k',\n", " 'seresnet18',\n", " 'seresnet33ts',\n", " 'seresnet34',\n", " 'seresnet50',\n", " 'seresnet50t',\n", " 'seresnet101',\n", " 'seresnet152',\n", " 'seresnet152d',\n", " 'seresnet200d',\n", " 'seresnet269d',\n", " 'skresnet18',\n", " 'skresnet34',\n", " 'skresnet50',\n", " 'skresnet50d',\n", " 'ssl_resnet18',\n", " 'ssl_resnet50',\n", " 'swsl_resnet18',\n", " 'swsl_resnet50',\n", " 'tresnet_l',\n", " 'tresnet_l_448',\n", " 'tresnet_m',\n", " 'tresnet_m_448',\n", " 'tresnet_m_miil_in21k',\n", " 'tresnet_xl',\n", " 'tresnet_xl_448',\n", " 'tv_resnet34',\n", " 'tv_resnet50',\n", " 'tv_resnet101',\n", " 'tv_resnet152',\n", " 'vit_base_resnet26d_224',\n", " 'vit_base_resnet50_224_in21k',\n", " 'vit_base_resnet50_384',\n", " 'vit_base_resnet50d_224',\n", " 'vit_small_resnet26d_224',\n", " 'vit_small_resnet50d_s16_224',\n", " 'wide_resnet50_2',\n", " 'wide_resnet101_2']"]}, "execution_count": 227, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用通配符查找resnet系列\n", "all_resnent_models = timm.list_models(\"*resnet*\")\n", "all_resnent_models"]}, {"cell_type": "code", "execution_count": 136, "id": "ffb5e723", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'url': 'https://download.pytorch.org/models/resnet18-5c106cde.pth',\n", " 'num_classes': 1000,\n", " 'input_size': (3, 224, 224),\n", " 'pool_size': (7, 7),\n", " 'crop_pct': 0.875,\n", " 'interpolation': 'bilinear',\n", " 'mean': (0.485, 0.456, 0.406),\n", " 'std': (0.229, 0.224, 0.225),\n", " 'first_conv': 'conv1',\n", " 'classifier': 'fc',\n", " 'architecture': 'resnet18'}"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看模型默认的cfg\n", "model.default_cfg"]}, {"cell_type": "code", "execution_count": 190, "id": "bf95f9f2", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from torch.optim import Adam\n", "from torch.optim.lr_scheduler import CosineAnnealingLR\n", "\n", "# 设置优化器和损失函数\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = Adam(model.parameters(), lr=3e-4, betas=[0.9, 0.99], eps=1e-08, weight_decay=0.0)\n", "scheduler = CosineAnnealingLR(optimizer, T_max=100, eta_min=0)\n", "# 模型训练\n", "def train(epoch,model,train_loader):\n", "    model.train()\n", "    train_loss = 0\n", "    for image, target in tqdm(train_loader):\n", "        image = image.float()\n", "        image = image.to(device)\n", "        target = target.to(device)\n", "        predict = model(image)\n", "        loss = criterion(predict, target)\n", "\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        scheduler.step()\n", "        train_loss += loss.item() * image.size(0)\n", "    train_loss = train_loss / len(train_loader.dataset)\n", "    print('Epoch: {} \\tTraining Loss: {:.6f}'.format(epoch, train_loss))"]}, {"cell_type": "code", "execution_count": 225, "id": "60fbe6de", "metadata": {}, "outputs": [], "source": ["# 测试集验证\n", "def val(epoch,model,val_dataloader):\n", "    model.eval()\n", "    val_loss = 0\n", "    gt_labels = []\n", "    pred_labels = []\n", "    with torch.no_grad():\n", "        for data, label in tqdm(val_dataloader):\n", "            data, label = data.float().to(device), label.to(device)\n", "            output = model(data)\n", "            preds = torch.argmax(output, 1)\n", "            gt_labels.append(label.cpu().data.numpy())\n", "            pred_labels.append(preds.cpu().data.numpy())\n", "            loss = criterion(output, label)\n", "            val_loss += loss.item() * data.size(0)\n", "    val_loss = val_loss / len(val_dataloader)\n", "    gt_labels, pred_labels = np.concatenate(gt_labels), np.concatenate(pred_labels)\n", "    acc = np.sum(gt_labels == pred_labels) / len(pred_labels)\n", "    print('Epoch: {} \\tValidation Loss: {:.6f}, Accuracy: {:6f}'.format(epoch, val_loss, acc))"]}, {"cell_type": "code", "execution_count": 226, "id": "c7e459c1", "metadata": {}, "outputs": [], "source": ["# 验证集验证\n", "def test(epoch,model,test_dataloader):\n", "    model.eval()\n", "    test_loss = 0\n", "    gt_labels = []\n", "    pred_labels = []\n", "    with torch.no_grad():\n", "        for data, label in tqdm(test_dataloader):\n", "            data, label = data.float().to(device), label.to(device)\n", "            output = model(data)\n", "            preds = torch.argmax(output, 1)\n", "            gt_labels.append(label.cpu().data.numpy())\n", "            pred_labels.append(preds.cpu().data.numpy())\n", "    gt_labels, pred_labels = np.concatenate(gt_labels), np.concatenate(pred_labels)\n", "    acc = np.sum(gt_labels == pred_labels) / len(pred_labels)\n", "    print('Epoch: {} \\tTest  Accuracy: {:6f}'.format(epoch, acc))"]}, {"cell_type": "markdown", "id": "cb9dbbbd", "metadata": {}, "source": ["以下仅展示训练和测试验证的结果。保存参数可以根据实际需求进行保存"]}, {"cell_type": "code", "execution_count": 211, "id": "79edafca", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:50<00:00,  1.74s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 1.017386\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:27<00:00,  2.49s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 27.227383, Accuracy: 0.746439\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:26<00:00,  2.20s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.738162\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:44<00:00,  1.68s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.844879\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:28<00:00,  2.57s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 10.465725, Accuracy: 0.897436\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:26<00:00,  2.20s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.905292\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:44<00:00,  1.68s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.582476\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:22<00:00,  2.03s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 15.013344, Accuracy: 0.871795\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:24<00:00,  2.06s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.844011\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:44<00:00,  1.67s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.632940\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:23<00:00,  2.17s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 7.386183, Accuracy: 0.928775\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:22<00:00,  1.91s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.908078\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:39<00:00,  1.63s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.415200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:26<00:00,  2.40s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 13.273417, Accuracy: 0.857550\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:26<00:00,  2.18s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.874652\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:40<00:00,  1.64s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.544968\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:24<00:00,  2.20s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 6.059515, Accuracy: 0.940171\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:24<00:00,  2.01s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.933148\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:47<00:00,  1.71s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.350375\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:22<00:00,  2.02s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 11.914146, Accuracy: 0.897436\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:23<00:00,  1.97s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.899721\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:37<00:00,  1.61s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.459682\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:24<00:00,  2.23s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 6.115790, Accuracy: 0.943020\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:25<00:00,  2.09s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.935933\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:31<00:00,  1.55s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.261108\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:24<00:00,  2.23s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 7.643102, Accuracy: 0.923077\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:24<00:00,  2.06s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.927577\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 98/98 [02:40<00:00,  1.64s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTraining Loss: 0.404539\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 11/11 [00:24<00:00,  2.27s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tValidation Loss: 7.167813, Accuracy: 0.934473\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 12/12 [00:27<00:00,  2.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: 1 \tTest  Accuracy: 0.941504\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for epoch in range(epochs):\n", "    train(epoch,model,train_loader)\n", "    val(epoch,model,val_loader)\n", "    test(epoch,model,test_loader)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}