{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/sphinx_book_theme/assets/styles/index.scss", "webpack:///./src/sphinx_book_theme/assets/scripts/index.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "sbRunWhenDOMLoaded", "cb", "document", "readyState", "addEventListener", "attachEvent", "window", "initThebeSBT", "title", "$", "next", "hasClass", "insertAfter", "initThebe", "printPdf", "el", "tooltipID", "attr", "tooltipTextDiv", "detach", "print", "append", "toggleFullScreen", "isInFullScreen", "fullscreenElement", "webkitFullscreenElement", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "console", "log", "exitFullscreen", "webkitExitFullscreen", "requestFullscreen", "webkitRequestFullscreen", "ready", "tooltip", "trigger", "delay", "show", "hide", "navbar", "getElementById", "active_pages", "querySelectorAll", "active_page", "length", "undefined", "offsetTop", "height", "scrollTop", "onScreenItems", "tocObserver", "IntersectionObserver", "entries", "observer", "for<PERSON>ach", "entry", "isIntersecting", "push", "target", "ii", "splice", "removeClass", "addClass", "marginSelector", "replace", "join", "observe", "boundingClientRect", "y", "body", "classList", "add", "remove", "querySelector", "MutationObserver", "mutationList", "mutation", "addedNodes", "data", "search", "node", "childList"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,sEClFtC,QCSXC,EAAsBC,IACG,WAAvBC,SAASC,WACXF,IACSC,SAASE,iBAClBF,SAASE,iBAAiB,mBAAoBH,GAE9CC,SAASG,YAAY,sBAAsB,WACd,YAAvBH,SAASC,YAA0BF,QAsM7CK,OAAOC,aAjDY,KACjB,IAAIC,EAAQC,EAAE,kBAAkB,GAC3BA,EAAED,GAAOE,OAAOC,SAAS,wBAC5BF,EAAE,iDAAiDG,YAAYH,EAAED,IAEnEK,aA6CFP,OAAOQ,SA7ISC,IAGd,IAAIC,EAAYP,EAAEM,GAAIE,KAAK,oBACvBC,EAAiBT,EAAE,IAAMO,GAAWG,SACxCb,OAAOc,QACPX,EAAE,QAAQY,OAAOH,IAwInBZ,OAAOgB,iBA5LgB,KACrB,IAAIC,EACDrB,SAASsB,mBAAoD,OAA/BtB,SAASsB,mBACvCtB,SAASuB,yBAC6B,OAArCvB,SAASuB,wBACb,IAAIC,EAASxB,SAASyB,gBACjBJ,GAQHK,QAAQC,IAAI,8BACR3B,SAAS4B,eACX5B,SAAS4B,iBACA5B,SAAS6B,sBAClB7B,SAAS6B,yBAXXH,QAAQC,IAAI,+BACRH,EAAOM,kBACTN,EAAOM,oBACEN,EAAOO,yBAChBP,EAAOO,4BAsLbjC,EA7CmB,KACjBS,EAAEP,UAAUgC,OAAM,WAChBzB,EAAE,2BAA2B0B,QAAQ,CACnCC,QAAS,QACTC,MAAO,CAAEC,KAAM,IAAKC,KAAM,YA0ChCvC,EArKqB,KACnB,IAAIwC,EAAStC,SAASuC,eAAe,mBACjCC,EAAeF,EAAOG,iBAAiB,WACvCC,EAAcF,EAAaA,EAAaG,OAAS,QAGnCC,IAAhBF,GACAA,EAAYG,UAAiC,GAArBtC,EAAEH,QAAQ0C,WAElCR,EAAOS,UAAYL,EAAYG,UAAiC,GAArBtC,EAAEH,QAAQ0C,YA6JzDhD,EA9HkB,KAChB,IAAIkD,EAAgB,GACpB,IAkCIC,EAAc,IAAIC,qBAlCA,CAACC,EAASC,KAE9BD,EAAQE,QAASC,IACf,GAAIA,EAAMC,eAERP,EAAcQ,KAAKF,EAAMG,aAGzB,IAAK,IAAIC,EAAK,EAAGA,EAAKV,EAAcL,OAAQe,IAC1C,GAAIV,EAAcU,KAAQJ,EAAMG,OAAQ,CACtCT,EAAcW,OAAOD,EAAI,GACzB,SAOJV,EAAcL,OAAS,EACzBpC,EAAE,cAAcqD,YAAY,QAE5BrD,EAAE,cAAcsD,SAAS,UAqB7B,IAAIC,EAAiB,GAPG,CACtB,SACA,iBACA,aACA,UACA,UAGcT,QAASK,IAEvBI,EAAeN,KAEX,IAAIE,EACJ,QAAQA,EACR,IAAIA,EAAGK,QAAQ,IAAK,KACpB,QAAQL,EAAGK,QAAQ,IAAK,QAI9B/D,SAASyC,iBAAiBqB,EAAeE,KAAK,OAAOX,QAASK,IAC5DT,EAAYgB,QAAQP,KAID,IAAIR,qBAnCO,CAACC,EAASC,KAEpCD,EAAQ,GAAGe,mBAAmBC,EAAI,EACpCnE,SAASoE,KAAKC,UAAUC,IAAI,YAE5BtE,SAASoE,KAAKC,UAAUE,OAAO,cA+BpBN,QAAQjE,SAASwE,cAAc,+BAiEhD1E,GApCA,WAkBmB,IAAI2E,iBAjBG,CAACC,EAActB,KACrCsB,EAAarB,QAASsB,IAEe,IAA/BA,EAASC,WAAWjC,aAGYC,IAAhC+B,EAASC,WAAW,GAAGC,OAGuC,GAA9DF,EAASC,WAAW,GAAGC,KAAKC,OAAO,wBACrCH,EAASC,WAAWvB,QAAS0B,IAC3B/E,SAASuC,eAAe,wBAAwBpB,OAAO4D,SAQtDd,QAAQjE,SAASoE,KADX,CAAEY,WAAW", "file": "scripts/sphinx-book-theme.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "export default __webpack_public_path__ + \"styles/sphinx-book-theme.css\";", "// Import CSS variables\n// ref: https://css-tricks.com/getting-javascript-to-talk-to-css-and-sass/\nimport \"../styles/index.scss\";\n\n/**\n * A helper function to load scripts when the DOM is loaded.\n * This waits for everything to be on the page first before running, since\n * some functionality doesn't behave properly until everything is ready.\n */\nvar sbRunWhenDOMLoaded = (cb) => {\n  if (document.readyState != \"loading\") {\n    cb();\n  } else if (document.addEventListener) {\n    document.addEventListener(\"DOMContentLoaded\", cb);\n  } else {\n    document.attachEvent(\"onreadystatechange\", function () {\n      if (document.readyState == \"complete\") cb();\n    });\n  }\n};\n\n/**\n * Toggle full-screen with button\n *\n * There are some browser-specific hacks in here:\n * - <PERSON><PERSON> requires a `webkit` prefix, so this uses conditionals to check for that\n *   ref: https://developer.mozilla.org/en-US/docs/Web/API/Fullscreen_API\n */\nvar toggleFullScreen = () => {\n  var isInFullScreen =\n    (document.fullscreenElement && document.fullscreenElement !== null) ||\n    (document.webkitFullscreenElement &&\n      document.webkitFullscreenElement !== null);\n  let docElm = document.documentElement;\n  if (!isInFullScreen) {\n    console.log(\"[SBT]: Entering full screen\");\n    if (docElm.requestFullscreen) {\n      docElm.requestFullscreen();\n    } else if (docElm.webkitRequestFullscreen) {\n      docElm.webkitRequestFullscreen();\n    }\n  } else {\n    console.log(\"[SBT]: Exiting full screen\");\n    if (document.exitFullscreen) {\n      document.exitFullscreen();\n    } else if (document.webkitExitFullscreen) {\n      document.webkitExitFullscreen();\n    }\n  }\n};\n\n/**\n * Sidebar scroll on load.\n *\n * Detect the active page in the sidebar, and scroll so that it is centered on\n * the screen.\n */\nvar scrollToActive = () => {\n  var navbar = document.getElementById(\"site-navigation\");\n  var active_pages = navbar.querySelectorAll(\".active\");\n  var active_page = active_pages[active_pages.length - 1];\n  // Only scroll the navbar if the active link is lower than 50% of the page\n  if (\n    active_page !== undefined &&\n    active_page.offsetTop > $(window).height() * 0.5\n  ) {\n    navbar.scrollTop = active_page.offsetTop - $(window).height() * 0.2;\n  }\n};\n\n/**\n * Called when the \"print to PDF\" button is clicked.\n * This is a hack to prevent tooltips from showing up in the printed PDF.\n */\nvar printPdf = (el) => {\n  // Detach the tooltip text from DOM to hide in PDF\n  // and then reattach it for HTML\n  let tooltipID = $(el).attr(\"aria-describedby\");\n  let tooltipTextDiv = $(\"#\" + tooltipID).detach();\n  window.print();\n  $(\"body\").append(tooltipTextDiv);\n};\n\n/**\n * Manage scrolling behavior. This is primarily two things:\n *\n * 1. Hide the Table of Contents any time sidebar content is on the screen.\n *\n * This will be triggered any time a sidebar item enters or exits the screen.\n * It adds/removes items from an array if they have entered the screen, and\n * removes them when they exit the screen. It hides the TOC if anything is\n * on-screen.\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API\n *\n * 2. Add a `scrolled` class to <body> to trigger CSS changes.\n */\nvar initTocHide = () => {\n  var onScreenItems = [];\n  let hideTocCallback = (entries, observer) => {\n    // Check whether any sidebar item is displayed\n    entries.forEach((entry) => {\n      if (entry.isIntersecting) {\n        // If an element just came on screen, add it our list\n        onScreenItems.push(entry.target);\n      } else {\n        // Otherwise, if it's in our list then remove it\n        for (let ii = 0; ii < onScreenItems.length; ii++) {\n          if (onScreenItems[ii] === entry.target) {\n            onScreenItems.splice(ii, 1);\n            break;\n          }\n        }\n      }\n    });\n\n    // Hide the TOC if any margin content is displayed on the screen\n    if (onScreenItems.length > 0) {\n      $(\"div.bd-toc\").removeClass(\"show\");\n    } else {\n      $(\"div.bd-toc\").addClass(\"show\");\n    }\n  };\n  let manageScrolledClassOnBody = (entries, observer) => {\n    // The pixel is at the top, so if we're < 0 that it means we've scrolled\n    if (entries[0].boundingClientRect.y < 0) {\n      document.body.classList.add(\"scrolled\");\n    } else {\n      document.body.classList.remove(\"scrolled\");\n    }\n  };\n\n  // Set up the intersection observer to watch all margin content\n  let tocObserver = new IntersectionObserver(hideTocCallback);\n  const selectorClasses = [\n    \"margin\",\n    \"margin-caption\",\n    \"full-width\",\n    \"sidebar\",\n    \"popout\",\n  ];\n  let marginSelector = [];\n  selectorClasses.forEach((ii) => {\n    // Use three permutations of each class name because `tag_` and `_` used to be supported\n    marginSelector.push(\n      ...[\n        `.${ii}`,\n        `.tag_${ii}`,\n        `.${ii.replace(\"-\", \"_\")}`,\n        `.tag_${ii.replace(\"-\", \"_\")}`,\n      ]\n    );\n  });\n  document.querySelectorAll(marginSelector.join(\", \")).forEach((ii) => {\n    tocObserver.observe(ii);\n  });\n\n  // Set up the observer to check if we've scrolled from top of page\n  let scrollObserver = new IntersectionObserver(manageScrolledClassOnBody);\n  scrollObserver.observe(document.querySelector(\".sbt-scroll-pixel-helper\"));\n};\n\n/**\n * Activate Thebe with a custom button click.\n */\nvar initThebeSBT = () => {\n  var title = $(\"div.section h1\")[0];\n  if (!$(title).next().hasClass(\"thebe-launch-button\")) {\n    $(\"<button class='thebe-launch-button'></button>\").insertAfter($(title));\n  }\n  initThebe();\n};\n\n/**\n * Use Bootstrap helper function to enable tooltips.\n */\nvar initTooltips = () => {\n  $(document).ready(function () {\n    $('[data-toggle=\"tooltip\"]').tooltip({\n      trigger: \"hover\",\n      delay: { show: 500, hide: 100 },\n    });\n  });\n};\n\n/**\n * MutationObserver to move the ReadTheDocs button\n */\nfunction initRTDObserver() {\n  const mutatedCallback = (mutationList, observer) => {\n    mutationList.forEach((mutation) => {\n      // Check whether the mutation is for RTD, which will have a specific structure\n      if (mutation.addedNodes.length === 0) {\n        return;\n      }\n      if (mutation.addedNodes[0].data === undefined) {\n        return;\n      }\n      if (mutation.addedNodes[0].data.search(\"Inserted RTD Footer\") != -1) {\n        mutation.addedNodes.forEach((node) => {\n          document.getElementById(\"rtd-footer-container\").append(node);\n        });\n      }\n    });\n  };\n\n  const observer = new MutationObserver(mutatedCallback);\n  const config = { childList: true };\n  observer.observe(document.body, config);\n}\n\n/**\n * Set up callback functions for UI click actions\n */\nwindow.initThebeSBT = initThebeSBT;\nwindow.printPdf = printPdf;\nwindow.toggleFullScreen = toggleFullScreen;\n\n/**\n * Set up functions to load when the DOM is ready\n */\nsbRunWhenDOMLoaded(initTooltips);\nsbRunWhenDOMLoaded(scrollToActive);\nsbRunWhenDOMLoaded(initTocHide);\nsbRunWhenDOMLoaded(initRTDObserver);\n"], "sourceRoot": ""}