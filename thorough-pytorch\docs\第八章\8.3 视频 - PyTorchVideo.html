
<!DOCTYPE html>

<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

    <title>8.3 PyTorchVideo简介 &#8212; 深入浅出PyTorch</title>
    
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">
<link href="../_static/styles/pydata-sphinx-theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">

    
  <link rel="stylesheet"
    href="../_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" href="../_static/styles/sphinx-book-theme.css?digest=62ba249389abaaa9ffc34bf36a076bdc1d65ee18" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../_static/togglebutton.css" />
    <link rel="stylesheet" type="text/css" href="../_static/mystnb.css" />
    <link rel="stylesheet" type="text/css" href="../_static/plot_directive.css" />
    
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf">

    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/doctools.js"></script>
    <script>let toggleHintShow = 'Click to show';</script>
    <script>let toggleHintHide = 'Click to hide';</script>
    <script>let toggleOpenOnPrint = 'true';</script>
    <script src="../_static/togglebutton.js"></script>
    <script src="../_static/scripts/sphinx-book-theme.js?digest=f31d14ad54b65d19161ba51d4ffff3a77ae00456"></script>
    <script>var togglebuttonSelector = '.toggle, .admonition.dropdown, .tag_hide_input div.cell_input, .tag_hide-input div.cell_input, .tag_hide_output div.cell_output, .tag_hide-output div.cell_output, .tag_hide_cell.cell, .tag_hide-cell.cell';</script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="8.4 torchtext简介" href="8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html" />
    <link rel="prev" title="8.2 torchvision" href="8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="docsearch:language" content="zh">
    

    <!-- Google Analytics -->
    
  </head>
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="60">
<!-- Checkboxes to toggle the left sidebar -->
<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation" aria-label="Toggle navigation sidebar">
<label class="overlay overlay-navbar" for="__navigation">
    <div class="visually-hidden">Toggle navigation sidebar</div>
</label>
<!-- Checkboxes to toggle the in-page toc -->
<input type="checkbox" class="sidebar-toggle" name="__page-toc" id="__page-toc" aria-label="Toggle in-page Table of Contents">
<label class="overlay overlay-pagetoc" for="__page-toc">
    <div class="visually-hidden">Toggle in-page Table of Contents</div>
</label>
<!-- Headers at the top -->
<div class="announcement header-item noprint"></div>
<div class="header header-item noprint"></div>

    
    <div class="container-fluid" id="banner"></div>

    

    <div class="container-xl">
      <div class="row">
          
<!-- Sidebar -->
<div class="bd-sidebar noprint" id="site-navigation">
    <div class="bd-sidebar__content">
        <div class="bd-sidebar__top"><div class="navbar-brand-box">
    <a class="navbar-brand text-wrap" href="../index.html">
      
      
      
      <h1 class="site-logo" id="site-title">深入浅出PyTorch</h1>
      
    </a>
</div><form class="bd-search d-flex align-items-center" action="../search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main">
    <div class="bd-toc-item active">
        <p aria-level="2" class="caption" role="heading">
 <span class="caption-text">
  目录
 </span>
</p>
<ul class="current nav bd-sidenav">
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/index.html">
   第零章：前置知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" type="checkbox"/>
  <label for="toctree-checkbox-1">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.1%20%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%AE%80%E5%8F%B2.html">
     人工智能简史
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.2%20%E8%AF%84%E4%BB%B7%E6%8C%87%E6%A0%87.html">
     模型评价指标
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html">
     常用包的学习
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.4%20Jupyter%E7%9B%B8%E5%85%B3%E6%93%8D%E4%BD%9C.html">
     Jupyter notebook/Lab 简述
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/index.html">
   第一章：PyTorch的简介和安装
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-2" name="toctree-checkbox-2" type="checkbox"/>
  <label for="toctree-checkbox-2">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.1%20PyTorch%E7%AE%80%E4%BB%8B.html">
     1.1 PyTorch简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.2%20PyTorch%E7%9A%84%E5%AE%89%E8%A3%85.html">
     1.2 PyTorch的安装
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html">
     1.3 PyTorch相关资源
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/index.html">
   第二章：PyTorch基础知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-3" name="toctree-checkbox-3" type="checkbox"/>
  <label for="toctree-checkbox-3">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.1%20%E5%BC%A0%E9%87%8F.html">
     2.1 张量
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.2%20%E8%87%AA%E5%8A%A8%E6%B1%82%E5%AF%BC.html">
     2.2 自动求导
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.3%20%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%E7%AE%80%E4%BB%8B.html">
     2.3 并行计算简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.4%20AI%E7%A1%AC%E4%BB%B6%E5%8A%A0%E9%80%9F%E8%AE%BE%E5%A4%87.html">
     AI硬件加速设备
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/index.html">
   第三章：PyTorch的主要组成模块
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-4" name="toctree-checkbox-4" type="checkbox"/>
  <label for="toctree-checkbox-4">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.1%20%E6%80%9D%E8%80%83%EF%BC%9A%E5%AE%8C%E6%88%90%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E7%9A%84%E5%BF%85%E8%A6%81%E9%83%A8%E5%88%86.html">
     3.1 思考：完成深度学习的必要部分
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.2%20%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE.html">
     3.2 基本配置
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.3%20%E6%95%B0%E6%8D%AE%E8%AF%BB%E5%85%A5.html">
     3.3 数据读入
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.4%20%E6%A8%A1%E5%9E%8B%E6%9E%84%E5%BB%BA.html">
     3.4 模型构建
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.5%20%E6%A8%A1%E5%9E%8B%E5%88%9D%E5%A7%8B%E5%8C%96.html">
     3.5 模型初始化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.6%20%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     3.6 损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.7%20%E8%AE%AD%E7%BB%83%E4%B8%8E%E8%AF%84%E4%BC%B0.html">
     3.7 训练和评估
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.8%20%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     3.8 可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.9%20%E4%BC%98%E5%8C%96%E5%99%A8.html">
     3.9 PyTorch优化器
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/index.html">
   第四章：PyTorch基础实战
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-5" name="toctree-checkbox-5" type="checkbox"/>
  <label for="toctree-checkbox-5">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.1%20ResNet.html">
     4.1 ResNet
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.4%20FashionMNIST%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     基础实战——FashionMNIST时装分类
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/index.html">
   第五章：PyTorch模型定义
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-6" name="toctree-checkbox-6" type="checkbox"/>
  <label for="toctree-checkbox-6">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.1%20PyTorch%E6%A8%A1%E5%9E%8B%E5%AE%9A%E4%B9%89%E7%9A%84%E6%96%B9%E5%BC%8F.html">
     5.1 PyTorch模型定义的方式
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.2%20%E5%88%A9%E7%94%A8%E6%A8%A1%E5%9E%8B%E5%9D%97%E5%BF%AB%E9%80%9F%E6%90%AD%E5%BB%BA%E5%A4%8D%E6%9D%82%E7%BD%91%E7%BB%9C.html">
     5.2 利用模型块快速搭建复杂网络
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.3%20PyTorch%E4%BF%AE%E6%94%B9%E6%A8%A1%E5%9E%8B.html">
     5.3 PyTorch修改模型
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.4%20PyTorh%E6%A8%A1%E5%9E%8B%E4%BF%9D%E5%AD%98%E4%B8%8E%E8%AF%BB%E5%8F%96.html">
     5.4 PyTorch模型保存与读取
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/index.html">
   第六章：PyTorch进阶训练技巧
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-7" name="toctree-checkbox-7" type="checkbox"/>
  <label for="toctree-checkbox-7">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.1%20%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     6.1 自定义损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.2%20%E5%8A%A8%E6%80%81%E8%B0%83%E6%95%B4%E5%AD%A6%E4%B9%A0%E7%8E%87.html">
     6.2 动态调整学习率
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-torchvision.html">
     6.3 模型微调-torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-timm.html">
     6.3 模型微调 - timm
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.4%20%E5%8D%8A%E7%B2%BE%E5%BA%A6%E8%AE%AD%E7%BB%83.html">
     6.4 半精度训练
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.5%20%E6%95%B0%E6%8D%AE%E5%A2%9E%E5%BC%BA-imgaug.html">
     6.5 数据增强-imgaug
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.6%20%E4%BD%BF%E7%94%A8argparse%E8%BF%9B%E8%A1%8C%E8%B0%83%E5%8F%82.html">
     6.6 使用argparse进行调参
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/index.html">
   第七章：PyTorch可视化
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-8" name="toctree-checkbox-8" type="checkbox"/>
  <label for="toctree-checkbox-8">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.1%20%E5%8F%AF%E8%A7%86%E5%8C%96%E7%BD%91%E7%BB%9C%E7%BB%93%E6%9E%84.html">
     7.1 可视化网络结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     7.2 CNN可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.3%20%E4%BD%BF%E7%94%A8TensorBoard%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.3 使用TensorBoard可视化训练过程
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%83%E7%AB%A0/7.4%20%E4%BD%BF%E7%94%A8wandb%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.4 使用wandb可视化训练过程
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 current active has-children">
  <a class="reference internal" href="index.html">
   第八章：PyTorch生态简介
  </a>
  <input checked="" class="toctree-checkbox" id="toctree-checkbox-9" name="toctree-checkbox-9" type="checkbox"/>
  <label for="toctree-checkbox-9">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul class="current">
   <li class="toctree-l2">
    <a class="reference internal" href="8.1%20%E6%9C%AC%E7%AB%A0%E7%AE%80%E4%BB%8B.html">
     8.1 本章简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html">
     8.2 torchvision
    </a>
   </li>
   <li class="toctree-l2 current active">
    <a class="current reference internal" href="#">
     8.3 PyTorchVideo简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html">
     8.4 torchtext简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="8.5%20%E9%9F%B3%E9%A2%91%20-%20torchaudio.html">
     8.5 torchaudio简介
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/index.html">
   第九章：PyTorch的模型部署
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-10" name="toctree-checkbox-10" type="checkbox"/>
  <label for="toctree-checkbox-10">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/9.1%20%E4%BD%BF%E7%94%A8ONNX%E8%BF%9B%E8%A1%8C%E9%83%A8%E7%BD%B2%E5%B9%B6%E6%8E%A8%E7%90%86.html">
     9.1 使用ONNX进行部署并推理
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/index.html">
   第十章：常见代码解读
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-11" name="toctree-checkbox-11" type="checkbox"/>
  <label for="toctree-checkbox-11">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.1%20%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     10.1 图像分类简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.2%20%E7%9B%AE%E6%A0%87%E6%A3%80%E6%B5%8B.html">
     目标检测简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.3%20%E5%9B%BE%E5%83%8F%E5%88%86%E5%89%B2.html">
     10.3 图像分割简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ResNet%E6%BA%90%E7%A0%81%E8%A7%A3%E8%AF%BB.html">
     ResNet源码解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/RNN%E8%AF%A6%E8%A7%A3%E5%8F%8A%E5%85%B6%E5%AE%9E%E7%8E%B0.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/LSTM%E8%A7%A3%E8%AF%BB%E5%8F%8A%E5%AE%9E%E6%88%98.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Transformer%20%E8%A7%A3%E8%AF%BB.html">
     Transformer 解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ViT%E8%A7%A3%E8%AF%BB.html">
     ViT解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Swin-Transformer%E8%A7%A3%E8%AF%BB.html">
     Swin Transformer解读
    </a>
   </li>
  </ul>
 </li>
</ul>

    </div>
</nav></div>
        <div class="bd-sidebar__bottom">
             <!-- To handle the deprecated key -->
            
            <div class="navbar_extra_footer">
            Theme by the <a href="https://ebp.jupyterbook.org">Executable Book Project</a>
            </div>
            
        </div>
    </div>
    <div id="rtd-footer-container"></div>
</div>


          


          
<!-- A tiny helper pixel to detect if we've scrolled -->
<div class="sbt-scroll-pixel-helper"></div>
<!-- Main content -->
<div class="col py-0 content-container">
    
    <div class="header-article row sticky-top noprint">
        



<div class="col py-1 d-flex header-article-main">
    <div class="header-article__left">
        
        <label for="__navigation"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="right"
title="Toggle navigation"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-bars"></i>
  </span>

</label>

        
    </div>
    <div class="header-article__right">
<button onclick="toggleFullScreen()"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="bottom"
title="Fullscreen mode"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-expand"></i>
  </span>

</button>

<div class="menu-dropdown menu-dropdown-repository-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Source repositories">
      <i class="fab fa-github"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Source repository"
>
  

<span class="headerbtn__icon-container">
  <i class="fab fa-github"></i>
  </span>
<span class="headerbtn__text-container">repository</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/issues/new?title=Issue%20on%20page%20%2F第八章/8.3 视频 - PyTorchVideo.html&body=Your%20issue%20content%20here."
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Open an issue"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-lightbulb"></i>
  </span>
<span class="headerbtn__text-container">open issue</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/edit/master/第八章/8.3 视频 - PyTorchVideo.md"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Edit this page"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-pencil-alt"></i>
  </span>
<span class="headerbtn__text-container">suggest edit</span>
</a>

      </li>
      
    </ul>
  </div>
</div>

<div class="menu-dropdown menu-dropdown-download-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Download this page">
      <i class="fas fa-download"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="../_sources/第八章/8.3 视频 - PyTorchVideo.md.txt"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Download source file"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file"></i>
  </span>
<span class="headerbtn__text-container">.md</span>
</a>

      </li>
      
      <li>
        
<button onclick="printPdf(this)"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="left"
title="Print to PDF"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file-pdf"></i>
  </span>
<span class="headerbtn__text-container">.pdf</span>
</button>

      </li>
      
    </ul>
  </div>
</div>
<label for="__page-toc"
  class="headerbtn headerbtn-page-toc"
  
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-list"></i>
  </span>

</label>

    </div>
</div>

<!-- Table of contents -->
<div class="col-md-3 bd-toc show noprint">
    <div class="tocsection onthispage pt-5 pb-3">
        <i class="fas fa-list"></i> Contents
    </div>
    <nav id="bd-toc-nav" aria-label="Page">
        <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id1">
   8.3.1 PyTorchVideo的主要部件和亮点
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id2">
   8.3.2 PyTorchVideo的安装
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#model-zoo-benchmark">
   8.3.3 Model zoo 和 benchmark
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#pytorchvideo-model-zoo">
   8.3.4 使用 PyTorchVideo model zoo
  </a>
 </li>
</ul>

    </nav>
</div>
    </div>
    <div class="article row">
        <div class="col pl-md-3 pl-lg-5 content-container">
            <!-- Table of contents that is only displayed when printing the page -->
            <div id="jb-print-docs-body" class="onlyprint">
                <h1>8.3 PyTorchVideo简介</h1>
                <!-- Table of contents -->
                <div id="print-main-content">
                    <div id="jb-print-toc">
                        
                        <div>
                            <h2> Contents </h2>
                        </div>
                        <nav aria-label="Page">
                            <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id1">
   8.3.1 PyTorchVideo的主要部件和亮点
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#id2">
   8.3.2 PyTorchVideo的安装
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#model-zoo-benchmark">
   8.3.3 Model zoo 和 benchmark
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#pytorchvideo-model-zoo">
   8.3.4 使用 PyTorchVideo model zoo
  </a>
 </li>
</ul>

                        </nav>
                    </div>
                </div>
            </div>
            <main id="main-content" role="main">
                
              <div>
                
  <section class="tex2jax_ignore mathjax_ignore" id="pytorchvideo">
<h1>8.3 PyTorchVideo简介<a class="headerlink" href="#pytorchvideo" title="永久链接至标题">#</a></h1>
<p><img alt="" src="../_images/logo2.jpg" /></p>
<p>近几年来，随着传播媒介和视频平台的发展，视频正在取代图片成为下一代的主流媒体，这也使得有关视频的深度学习模型正在获得越来越多的关注。然而，有关视频的深度学习模型仍然有着许多缺点：</p>
<ul class="simple">
<li><p>计算资源耗费更多，并且没有高质量的<code class="docutils literal notranslate"><span class="pre">model</span> <span class="pre">zoo</span></code>，不能像图片一样进行迁移学习和论文复现。</p></li>
<li><p>数据集处理较麻烦，但没有一个很好的视频处理工具。</p></li>
<li><p>随着多模态越来越流行，亟需一个工具来处理其他模态。</p></li>
</ul>
<p>除此之外，还有部署优化等问题，为了解决这些问题，Meta推出了<code class="docutils literal notranslate"><span class="pre">PyTorchVideo</span></code>深度学习库（包含组件如Figure 1所示）。PyTorchVideo 是一个专注于视频理解工作的深度学习库。PytorchVideo 提供了加速视频理解研究所需的可重用、模块化和高效的组件。PyTorchVideo 是使用<a class="reference external" href="https://pytorch.org/">PyTorch</a>开发的，支持不同的深度学习视频组件，如视频模型、视频数据集和视频特定转换。</p>
<p><img alt="" src="../_images/list.png" /></p>
<section id="id1">
<h2>8.3.1 PyTorchVideo的主要部件和亮点<a class="headerlink" href="#id1" title="永久链接至标题">#</a></h2>
<p>PytorchVideo 提供了加速视频理解研究所需的模块化和高效的API。它还支持不同的深度学习视频组件，如视频模型、视频数据集和视频特定转换，最重要的是，PytorchVideo也提供了model zoo，使得人们可以使用各种先进的预训练视频模型及其评判基准。PyTorchVideo主要亮点如下：</p>
<ul class="simple">
<li><p>**基于 PyTorch：**使用 PyTorch 构建。使所有 PyTorch 生态系统组件的使用变得容易。</p></li>
<li><p>**Model Zoo：**PyTorchVideo提供了包含I3D、R(2+1)D、SlowFast、X3D、MViT等SOTA模型的高质量model zoo（目前还在快速扩充中，未来会有更多SOTA model），并且PyTorchVideo的model zoo调用与<a class="reference external" href="https://link.zhihu.com/?target=https%3A//pytorch.org/hub/">PyTorch Hub</a>做了整合，大大简化模型调用，具体的一些调用方法可以参考下面的【使用 PyTorchVideo model zoo】部分。</p></li>
<li><p><strong>数据预处理和常见数据</strong>，PyTorchVideo支持Kinetics-400, Something-Something V2, Charades, Ava (v2.2), Epic Kitchen, HMDB51, UCF101, Domsev等主流数据集和相应的数据预处理，同时还支持randaug, augmix等数据增强trick。</p></li>
<li><p><strong>模块化设计</strong>：PyTorchVideo的设计类似于torchvision，也是提供许多模块方便用户调用修改，在PyTorchVideo中具体来说包括data, transforms, layer, model, accelerator等模块，方便用户进行调用和读取。</p></li>
<li><p><strong>支持多模态</strong>：PyTorchVideo现在对多模态的支持包括了visual和audio，未来会支持更多模态，为多模态模型的发展提供支持。</p></li>
<li><p><strong>移动端部署优化</strong>：PyTorchVideo支持针对移动端模型的部署优化（使用前述的PyTorchVideo/accelerator模块），模型经过PyTorchVideo优化了最高达<strong>7倍</strong>的提速，并实现了第一个能实时跑在手机端的X3D模型（实验中可以实时跑在2018年的三星Galaxy S8上，具体请见<a class="reference external" href="https://github.com/pytorch/android-demo-app/tree/master/TorchVideo">Android Demo APP</a>）。</p></li>
</ul>
</section>
<section id="id2">
<h2>8.3.2 PyTorchVideo的安装<a class="headerlink" href="#id2" title="永久链接至标题">#</a></h2>
<p>我们可以直接使用pip来安装PyTorchVideo：</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>pip install pytorchvideo
</pre></div>
</div>
<p>注：</p>
<ul class="simple">
<li><p>安装的虚拟环境的python版本 &gt;= 3.7</p></li>
<li><p>PyTorch &gt;= 1.8.0，安装的torchvision也需要匹配</p></li>
<li><p>CUDA &gt;= 10.2</p></li>
<li><p>ioPath：<a class="reference external" href="https://github.com/facebookresearch/iopath">具体情况</a></p></li>
<li><p>fvcore版本 &gt;= 0.1.4：<a class="reference external" href="https://github.com/facebookresearch/fvcore">具体情况</a></p></li>
</ul>
</section>
<section id="model-zoo-benchmark">
<h2>8.3.3 Model zoo 和 benchmark<a class="headerlink" href="#model-zoo-benchmark" title="永久链接至标题">#</a></h2>
<p>在下面这部分，我将简单介绍些PyTorchVideo所提供的Model zoo和benchmark</p>
<ul class="simple">
<li><p>Kinetics-400</p></li>
</ul>
<table class="colwidths-auto table">
<thead>
<tr class="row-odd"><th class="head"><p>arch</p></th>
<th class="head"><p>depth</p></th>
<th class="head"><p>pretrain</p></th>
<th class="head"><p>frame length x sample rate</p></th>
<th class="head"><p>top 1</p></th>
<th class="head"><p>top 5</p></th>
<th class="head"><p>Flops (G) x views</p></th>
<th class="head"><p>Params (M)</p></th>
<th class="head"><p>Model</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>C2D</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>8x8</p></td>
<td><p>71.46</p></td>
<td><p>89.68</p></td>
<td><p>25.89 x 3 x 10</p></td>
<td><p>24.33</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/C2D%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>I3D</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>8x8</p></td>
<td><p>73.27</p></td>
<td><p>90.70</p></td>
<td><p>37.53 x 3 x 10</p></td>
<td><p>28.04</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/I3D%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>Slow</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>4x16</p></td>
<td><p>72.40</p></td>
<td><p>90.18</p></td>
<td><p>27.55 x 3 x 10</p></td>
<td><p>32.45</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOW%5C_4x16%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>Slow</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>8x8</p></td>
<td><p>74.58</p></td>
<td><p>91.63</p></td>
<td><p>54.52 x 3 x 10</p></td>
<td><p>32.45</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOW%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>SlowFast</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>4x16</p></td>
<td><p>75.34</p></td>
<td><p>91.89</p></td>
<td><p>36.69 x 3 x 10</p></td>
<td><p>34.48</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST%5C_4x16%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>SlowFast</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>8x8</p></td>
<td><p>76.94</p></td>
<td><p>92.69</p></td>
<td><p>65.71 x 3 x 10</p></td>
<td><p>34.57</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>SlowFast</p></td>
<td><p>R101</p></td>
<td><p>-</p></td>
<td><p>8x8</p></td>
<td><p>77.90</p></td>
<td><p>93.27</p></td>
<td><p>127.20 x 3 x 10</p></td>
<td><p>62.83</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST%5C_8x8%5C_R101.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>SlowFast</p></td>
<td><p>R101</p></td>
<td><p>-</p></td>
<td><p>16x8</p></td>
<td><p>78.70</p></td>
<td><p>93.61</p></td>
<td><p>215.61 x 3 x 10</p></td>
<td><p>53.77</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/SLOWFAST%5C_16x8%5C_R101_50_50.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>CSN</p></td>
<td><p>R101</p></td>
<td><p>-</p></td>
<td><p>32x2</p></td>
<td><p>77.00</p></td>
<td><p>92.90</p></td>
<td><p>75.62 x 3 x 10</p></td>
<td><p>22.21</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/CSN%5C_32x2%5C_R101.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>R(2+1)D</p></td>
<td><p>R50</p></td>
<td><p>-</p></td>
<td><p>16x4</p></td>
<td><p>76.01</p></td>
<td><p>92.23</p></td>
<td><p>76.45 x 3 x 10</p></td>
<td><p>28.11</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/R2PLUS1D%5C_16x4%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>X3D</p></td>
<td><p>XS</p></td>
<td><p>-</p></td>
<td><p>4x12</p></td>
<td><p>69.12</p></td>
<td><p>88.63</p></td>
<td><p>0.91 x 3 x 10</p></td>
<td><p>3.79</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D%5C_XS.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>X3D</p></td>
<td><p>S</p></td>
<td><p>-</p></td>
<td><p>13x6</p></td>
<td><p>73.33</p></td>
<td><p>91.27</p></td>
<td><p>2.96 x 3 x 10</p></td>
<td><p>3.79</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D%5C_S.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>X3D</p></td>
<td><p>M</p></td>
<td><p>-</p></td>
<td><p>16x5</p></td>
<td><p>75.94</p></td>
<td><p>92.72</p></td>
<td><p>6.72 x 3 x 10</p></td>
<td><p>3.79</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D%5C_M.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>X3D</p></td>
<td><p>L</p></td>
<td><p>-</p></td>
<td><p>16x5</p></td>
<td><p>77.44</p></td>
<td><p>93.31</p></td>
<td><p>26.64 x 3 x 10</p></td>
<td><p>6.15</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/X3D%5C_L.pyth">link</a></p></td>
</tr>
<tr class="row-even"><td><p>MViT</p></td>
<td><p>B</p></td>
<td><p>-</p></td>
<td><p>16x4</p></td>
<td><p>78.85</p></td>
<td><p>93.85</p></td>
<td><p>70.80 x 1 x 5</p></td>
<td><p>36.61</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/MVIT%5C_B%5C_16x4.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>MViT</p></td>
<td><p>B</p></td>
<td><p>-</p></td>
<td><p>32x3</p></td>
<td><p>80.30</p></td>
<td><p>94.69</p></td>
<td><p>170.37 x 1 x 5</p></td>
<td><p>36.61</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/MVIT%5C_B%5C_32x3%5C_f294077834.pyth">link</a></p></td>
</tr>
</tbody>
</table>
<ul class="simple">
<li><p>Something-Something V2</p></li>
</ul>
<table class="colwidths-auto table">
<thead>
<tr class="row-odd"><th class="head"><p>arch</p></th>
<th class="head"><p>depth</p></th>
<th class="head"><p>pretrain</p></th>
<th class="head"><p>frame length x sample rate</p></th>
<th class="head"><p>top 1</p></th>
<th class="head"><p>top 5</p></th>
<th class="head"><p>Flops (G) x views</p></th>
<th class="head"><p>Params (M)</p></th>
<th class="head"><p>Model</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Slow</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>8x8</p></td>
<td><p>60.04</p></td>
<td><p>85.19</p></td>
<td><p>55.10 x 3 x 1</p></td>
<td><p>31.96</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ssv2/SLOW%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>SlowFast</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>8x8</p></td>
<td><p>61.68</p></td>
<td><p>86.92</p></td>
<td><p>66.60 x 3 x 1</p></td>
<td><p>34.04</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ssv2/SLOWFAST%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
</tbody>
</table>
<ul class="simple">
<li><p>Charades</p></li>
</ul>
<table class="colwidths-auto table">
<thead>
<tr class="row-odd"><th class="head"><p>arch</p></th>
<th class="head"><p>depth</p></th>
<th class="head"><p>pretrain</p></th>
<th class="head"><p>frame length x sample rate</p></th>
<th class="head"><p>MAP</p></th>
<th class="head"><p>Flops (G) x views</p></th>
<th class="head"><p>Params (M)</p></th>
<th class="head"><p>Model</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Slow</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>8x8</p></td>
<td><p>34.72</p></td>
<td><p>55.10 x 3 x 10</p></td>
<td><p>31.96</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/charades/SLOW%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>SlowFast</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>8x8</p></td>
<td><p>37.24</p></td>
<td><p>66.60 x 3 x 10</p></td>
<td><p>34.00</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/charades/SLOWFAST%5C_8x8%5C_R50.pyth">link</a></p></td>
</tr>
</tbody>
</table>
<ul class="simple">
<li><p>AVA (V2.2)</p></li>
</ul>
<table class="colwidths-auto table">
<thead>
<tr class="row-odd"><th class="head"><p>arch</p></th>
<th class="head"><p>depth</p></th>
<th class="head"><p>pretrain</p></th>
<th class="head"><p>frame length x sample rate</p></th>
<th class="head"><p>MAP</p></th>
<th class="head"><p>Params (M)</p></th>
<th class="head"><p>Model</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Slow</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>4x16</p></td>
<td><p>19.5</p></td>
<td><p>31.78</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ava/SLOW%5C_4x16%5C_R50%5C_DETECTION.pyth">link</a></p></td>
</tr>
<tr class="row-odd"><td><p>SlowFast</p></td>
<td><p>R50</p></td>
<td><p>Kinetics 400</p></td>
<td><p>8x8</p></td>
<td><p>24.67</p></td>
<td><p>33.82</p></td>
<td><p><a class="reference external" href="https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/ava/SLOWFAST%5C_8x8%5C_R50%5C_DETECTION.pyth">link</a></p></td>
</tr>
</tbody>
</table>
</section>
<section id="pytorchvideo-model-zoo">
<h2>8.3.4 使用 PyTorchVideo model zoo<a class="headerlink" href="#pytorchvideo-model-zoo" title="永久链接至标题">#</a></h2>
<p>PyTorchVideo提供了三种使用方法，并且给每一种都配备了<code class="docutils literal notranslate"><span class="pre">tutorial</span></code></p>
<ul class="simple">
<li><p>TorchHub，这些模型都已经在TorchHub存在。我们可以根据实际情况来选择需不需要使用预训练模型。除此之外，官方也给出了TorchHub使用的 <a class="reference external" href="https://pytorchvideo.org/docs/tutorial_torchhub_inference">tutorial</a> 。</p></li>
<li><p>PySlowFast，使用 <a class="reference external" href="https://github.com/facebookresearch/SlowFast/">PySlowFast workflow</a> 去训练或测试PyTorchVideo models/datasets.</p></li>
<li><p><a class="reference external" href="https://github.com/PyTorchLightning/pytorch-lightning">PyTorch Lightning</a>建立一个工作流进行处理，点击查看官方 <a class="reference external" href="https://pytorchvideo.org/docs/tutorial_classification">tutorial</a>。</p></li>
</ul>
<ul class="simple">
<li><p>如果想查看更多的使用教程，可以点击 <a class="reference external" href="https://github.com/facebookresearch/pytorchvideo/tree/main/tutorials">这里</a> 进行尝试</p></li>
</ul>
<p>总的来说，PyTorchVideo的使用与torchvision的使用方法类似，在有了前面的学习基础上，我们可以很快上手PyTorchVideo，具体的我们可以通过查看官方提供的文档和一些例程来了解使用方法：<a class="reference external" href="https://pytorchvideo.readthedocs.io/en/latest/index.html">官方网址</a></p>
</section>
</section>


              </div>
              
            </main>
            <footer class="footer-article noprint">
                
    <!-- Previous / next buttons -->
<div class='prev-next-area'>
    <a class='left-prev' id="prev-link" href="8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html" title="上一页 页">
        <i class="fas fa-angle-left"></i>
        <div class="prev-next-info">
            <p class="prev-next-subtitle">上一页</p>
            <p class="prev-next-title">8.2 torchvision</p>
        </div>
    </a>
    <a class='right-next' id="next-link" href="8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html" title="下一页 页">
    <div class="prev-next-info">
        <p class="prev-next-subtitle">下一页</p>
        <p class="prev-next-title">8.4 torchtext简介</p>
    </div>
    <i class="fas fa-angle-right"></i>
    </a>
</div>
            </footer>
        </div>
    </div>
    <div class="footer-content row">
        <footer class="col footer"><p>
  
    By ZhikangNiu<br/>
  
      &copy; Copyright 2022, ZhikangNiu.<br/>
</p>
        </footer>
    </div>
    
</div>


      </div>
    </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf"></script>


  </body>
</html>