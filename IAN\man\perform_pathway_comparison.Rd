% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_pathway_comparison.R
\name{perform_pathway_comparison}
\alias{perform_pathway_comparison}
\title{IAN_pathway_comparison.R}
\usage{
perform_pathway_comparison(
  gene_symbols,
  kegg_results,
  wp_results,
  reactome_results,
  go_results,
  output_dir = "enrichment_results"
)
}
\arguments{
\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{kegg_results}{A data frame containing KEGG enrichment results.}

\item{wp_results}{A data frame containing WikiPathways enrichment results.}

\item{reactome_results}{A data frame containing Reactome enrichment results.}

\item{go_results}{A data frame containing GO enrichment results.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A list containing the pathway comparison results, including overlapping and unique genes for each pathway database. Returns a list with an "error" element if the analysis fails.
}
\description{
Performs a comparison of pathway enrichment results from KEGG, WikiPathways, Reactome, and GO analyses.
Identifies overlapping and unique genes across the different pathway databases.
}
