{"cells": [{"cell_type": "code", "execution_count": 305, "id": "75a7c6ac", "metadata": {}, "outputs": [], "source": ["# 增加，减少，剪切，保存，删除等\n", "# a, b, x, s, dd"]}, {"cell_type": "code", "execution_count": 301, "id": "e15057a3", "metadata": {}, "outputs": [], "source": ["# 合并，执行\n", "# shift+M shift+Enter"]}, {"cell_type": "code", "execution_count": 304, "id": "4c79aafc", "metadata": {}, "outputs": [], "source": ["# 显示行数，切换markdown/code\n", "# l, m/y"]}, {"cell_type": "markdown", "id": "e58949b2", "metadata": {"tags": []}, "source": ["## Bash命令"]}, {"cell_type": "markdown", "id": "9b24fb9b", "metadata": {}, "source": ["*斜体*\n", "\n", "_斜体2_\n", "\n", "**加粗字体**\n", "\n", "`import numpy as np # 单行代码`\n", "\n", "```json\n", "# 多行格式\n", "\n", "{\n", "    \"name\": \"Yam\",\n", "    \"age\": 22\n", "}\n", "\n", "```\n", "\n", "> 引用\n", "\n", "\n", "列表：\n", "\n", "- 第一个\n", "- 第二个\n", "\n", "TODO：\n", "\n", "- [x] 第一个\n", "- [ ] 第二个\n", "\n", "\n", "\n", "图片：![图片](https://img1.baidu.com/it/u=197141080,635147828&fm=253&fmt=auto&app=138&f=JPEG?w=150&h=113)\n", "\n", "[链接](https://yam.gift)\n", "\n", "\n", "表格：\n", "\n", "\n", "name|age\n", "---|---\n", "Yam|22"]}, {"cell_type": "code", "execution_count": 117, "id": "b82d9437", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Basic.ipynb    NumPy.ipynb    \u001b[1m\u001b[36mdata\u001b[m\u001b[m           \u001b[1m\u001b[36mexit\u001b[m\u001b[m\n", "Notebook.ipynb \u001b[1m\u001b[36m__pycache__\u001b[m\u001b[m    demo.py        \u001b[1m\u001b[36mtianchi\u001b[m\u001b[m\n"]}], "source": ["!ls"]}, {"cell_type": "code", "execution_count": 11, "id": "17cc63c4", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["organization\testablished\tceo\n", "Google\t1998\tSundar Pi<PERSON>i\n", "Microsoft\t1975\t<PERSON><PERSON><PERSON>\n", "Nokia\t1865\t<PERSON><PERSON><PERSON>"]}], "source": ["!head data/table.txt"]}, {"cell_type": "code", "execution_count": 12, "id": "33b8898a", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       8\n"]}], "source": ["!ls | wc -l"]}, {"cell_type": "code", "execution_count": 13, "id": "f6526311", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def add(a: int, b: int) -> int:\n", "    return a + b\n", "\n", "\n", "a = 10\n", "b = 10\n", "res = add(a, b)\n", "print(res)\n"]}], "source": ["!cat demo.py"]}, {"cell_type": "code", "execution_count": 18, "id": "d253b6df", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n"]}], "source": ["!python3.8 demo.py"]}, {"cell_type": "code", "execution_count": 17, "id": "a22ed1a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/usr/bin/python\n"]}], "source": ["!which python"]}, {"cell_type": "code", "execution_count": null, "id": "8dad0488", "metadata": {}, "outputs": [], "source": ["!pip install ..."]}, {"cell_type": "markdown", "id": "1e17015e", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## Magic"]}, {"cell_type": "code", "execution_count": 108, "id": "4351a7b1", "metadata": {}, "outputs": [{"data": {"application/json": {"cell": {"!": "OSMagics", "HTML": "Other", "SVG": "Other", "bash": "Other", "capture": "ExecutionMagics", "debug": "ExecutionMagics", "file": "Other", "html": "DisplayMagics", "javascript": "DisplayMagics", "js": "DisplayMagics", "latex": "DisplayMagics", "markdown": "DisplayMagics", "perl": "Other", "prun": "ExecutionMagics", "pypy": "Other", "python": "Other", "python2": "Other", "python3": "Other", "ruby": "Other", "script": "ScriptMagics", "sh": "Other", "svg": "DisplayMagics", "sx": "OSMagics", "system": "OSMagics", "time": "ExecutionMagics", "timeit": "ExecutionMagics", "writefile": "OSMagics"}, "line": {"alias": "OSMagics", "alias_magic": "BasicMagics", "autoawait": "AsyncMagics", "autocall": "AutoMagics", "automagic": "AutoMagics", "autosave": "KernelMagics", "bookmark": "OSMagics", "cat": "Other", "cd": "OSMagics", "clear": "KernelMagics", "colors": "BasicMagics", "conda": "PackagingMagics", "config": "ConfigMagics", "connect_info": "KernelMagics", "cp": "Other", "debug": "ExecutionMagics", "dhist": "OSMagics", "dirs": "OSMagics", "doctest_mode": "BasicMagics", "ed": "Other", "edit": "KernelMagics", "env": "OSMagics", "gui": "BasicMagics", "hist": "Other", "history": "HistoryMagics", "killbgscripts": "ScriptMagics", "ldir": "Other", "less": "KernelMagics", "lf": "Other", "lk": "Other", "ll": "Other", "load": "CodeMagics", "load_ext": "ExtensionMagics", "loadpy": "CodeMagics", "logoff": "LoggingMagics", "logon": "LoggingMagics", "logstart": "LoggingMagics", "logstate": "LoggingMagics", "logstop": "LoggingMagics", "ls": "Other", "lsmagic": "BasicMagics", "lx": "Other", "macro": "ExecutionMagics", "magic": "BasicMagics", "man": "KernelMagics", "matplotlib": "PylabMagics", "mkdir": "Other", "more": "KernelMagics", "mv": "Other", "notebook": "BasicMagics", "page": "BasicMagics", "pastebin": "CodeMagics", "pdb": "ExecutionMagics", "pdef": "NamespaceMagics", "pdoc": "NamespaceMagics", "pfile": "NamespaceMagics", "pinfo": "NamespaceMagics", "pinfo2": "NamespaceMagics", "pip": "PackagingMagics", "popd": "OSMagics", "pprint": "BasicMagics", "precision": "BasicMagics", "prun": "ExecutionMagics", "psearch": "NamespaceMagics", "psource": "NamespaceMagics", "pushd": "OSMagics", "pwd": "OSMagics", "pycat": "OSMagics", "pylab": "PylabMagics", "qtconsole": "KernelMagics", "quickref": "BasicMagics", "recall": "HistoryMagics", "rehashx": "OSMagics", "reload_ext": "ExtensionMagics", "rep": "Other", "rerun": "HistoryMagics", "reset": "NamespaceMagics", "reset_selective": "NamespaceMagics", "rm": "Other", "rmdir": "Other", "run": "ExecutionMagics", "save": "CodeMagics", "sc": "OSMagics", "set_env": "OSMagics", "store": "StoreMagics", "sx": "OSMagics", "system": "OSMagics", "tb": "ExecutionMagics", "time": "ExecutionMagics", "timeit": "ExecutionMagics", "unalias": "OSMagics", "unload_ext": "ExtensionMagics", "who": "NamespaceMagics", "who_ls": "NamespaceMagics", "whos": "NamespaceMagics", "xdel": "NamespaceMagics", "xmode": "BasicMagics"}}, "text/plain": ["Available line magics:\n", "%alias  %alias_magic  %autoawait  %autocall  %automagic  %autosave  %bookmark  %cat  %cd  %clear  %colors  %conda  %config  %connect_info  %cp  %debug  %dhist  %dirs  %doctest_mode  %ed  %edit  %env  %gui  %hist  %history  %killbgscripts  %ldir  %less  %lf  %lk  %ll  %load  %load_ext  %loadpy  %logoff  %logon  %logstart  %logstate  %logstop  %ls  %lsmagic  %lx  %macro  %magic  %man  %matplotlib  %mkdir  %more  %mv  %notebook  %page  %pastebin  %pdb  %pdef  %pdoc  %pfile  %pinfo  %pinfo2  %pip  %popd  %pprint  %precision  %prun  %psearch  %psource  %pushd  %pwd  %pycat  %pylab  %qtconsole  %quickref  %recall  %rehashx  %reload_ext  %rep  %rerun  %reset  %reset_selective  %rm  %rmdir  %run  %save  %sc  %set_env  %store  %sx  %system  %tb  %time  %timeit  %unalias  %unload_ext  %who  %who_ls  %whos  %xdel  %xmode\n", "\n", "Available cell magics:\n", "%%!  %%HTML  %%SVG  %%bash  %%capture  %%debug  %%file  %%html  %%javascript  %%js  %%latex  %%markdown  %%perl  %%prun  %%pypy  %%python  %%python2  %%python3  %%ruby  %%script  %%sh  %%svg  %%sx  %%system  %%time  %%timeit  %%writefile\n", "\n", "Automagic is ON, % prefix IS NOT needed for line magics."]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["# 列出魔法方法\n", "%lsmagic"]}, {"cell_type": "code", "execution_count": 109, "id": "c3c1945a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["env: PROFILE=dev\n"]}], "source": ["# 设置环境变量\n", "%env PROFILE=dev"]}, {"cell_type": "code", "execution_count": 114, "id": "542a857a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dev\n"]}], "source": ["!echo $PROFILE"]}, {"cell_type": "code", "execution_count": 115, "id": "80b0ba43", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 116, "id": "a9327d1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'dev'"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ.get(\"PROFILE\")"]}, {"cell_type": "markdown", "id": "3245e771", "metadata": {}, "source": ["最有用/常用的是timeit，用来评估运行效率。接下来，我们用两个排序的例子来说明。"]}, {"cell_type": "code", "execution_count": 307, "id": "ee956ed9", "metadata": {}, "outputs": [], "source": ["def choose_sort(arr: list) -> list:\n", "    length = len(arr)\n", "    for i in range(length):\n", "        min_idx = i\n", "        for j in range(i, length):\n", "            if arr[j] < arr[min_idx]:\n", "                min_idx = j\n", "        if min_idx != i:\n", "            arr[i], arr[min_idx] = arr[min_idx], arr[i]\n", "    return arr\n", "\n", "def quick_sort(arr: list) -> list:\n", "    \n", "    def qs(arr, l, r):\n", "        if l < r:\n", "            p = partion(arr, l, r)\n", "            qs(arr, l, p-1)\n", "            qs(arr, p, r)\n", "\n", "    def partion(arr: list, l: int, r: int) -> int:\n", "        pivot = arr[l + (r-l)//2]\n", "        while l<= r:\n", "            while arr[l] < pivot:\n", "                l += 1\n", "            while arr[r] > pivot:\n", "                r -= 1\n", "            if l <= r:\n", "                arr[l], arr[r] = arr[r], arr[l]\n", "                l += 1\n", "                r -= 1\n", "        return l\n", "    l, r = 0, len(arr) - 1\n", "    qs(arr, l, r)\n", "    return arr"]}, {"cell_type": "code", "execution_count": 308, "id": "47797997", "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 309, "id": "26584381", "metadata": {}, "outputs": [], "source": ["lst = [random.randint(1, 1000) for i in range(1000)]"]}, {"cell_type": "code", "execution_count": 288, "id": "d09a31e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["43 ms ± 2.3 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%timeit choose_sort(lst)"]}, {"cell_type": "code", "execution_count": 289, "id": "19bee478", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.79 ms ± 51.2 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["%timeit quick_sort(lst)"]}, {"cell_type": "code", "execution_count": 290, "id": "3b72790e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.65 µs ± 270 ns per loop (mean ± std. dev. of 7 runs, 100000 loops each)\n"]}], "source": ["%timeit sorted(lst)"]}, {"cell_type": "code", "execution_count": 315, "id": "f9b46d51", "metadata": {}, "outputs": [], "source": ["# 两个%表示多行，且 %%timeie 必须放在第一行"]}, {"cell_type": "code", "execution_count": 314, "id": "f86cdc72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["44.8 ms ± 5.06 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%%timeit\n", "\n", "choose_sort(lst)\n", "sorted(lst)"]}, {"cell_type": "markdown", "id": "6a4c127a", "metadata": {}, "source": ["此外，还有按执行代码单行分析，内存分析等功能。具体可查看参考文献【2】。"]}, {"cell_type": "markdown", "id": "f92229f7", "metadata": {"tags": []}, "source": ["## 笔记本 or 草稿纸？"]}, {"cell_type": "markdown", "id": "446e0d74", "metadata": {}, "source": ["我们可以随意执行任意代码，就像你在草稿纸上打草稿一样。"]}, {"cell_type": "code", "execution_count": 292, "id": "0a10442c", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 292, "metadata": {}, "output_type": "execute_result"}], "source": ["1 + 2"]}, {"cell_type": "code", "execution_count": 293, "id": "99c73084", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["27"]}, "execution_count": 293, "metadata": {}, "output_type": "execute_result"}], "source": ["3 ** 3"]}, {"cell_type": "code", "execution_count": 1, "id": "a9eecf94", "metadata": {}, "outputs": [], "source": ["x = [1, 2, 3]\n", "y = [4, 5, 6]"]}, {"cell_type": "code", "execution_count": 2, "id": "43f5aab7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(y + x)"]}, {"cell_type": "code", "execution_count": 4, "id": "55d5dc70", "metadata": {}, "outputs": [], "source": ["# 随时随地可查看接口文档\n", "x.append?"]}, {"cell_type": "code", "execution_count": null, "id": "86fba6ab", "metadata": {}, "outputs": [], "source": ["# tab 补全\n", "x."]}, {"cell_type": "markdown", "id": "974f9373", "metadata": {}, "source": ["再来点文本，注意，它支持Markdown格式。"]}, {"cell_type": "markdown", "id": "2c7a88b4", "metadata": {}, "source": ["也支持LaTeX格式的数学公式。"]}, {"cell_type": "markdown", "id": "89d0667c", "metadata": {}, "source": ["$$\n", "P(A|B) = \\frac{P(B|A)P(A)}{P(B)} \\\\\n", "e^{i \\pi} = 0\n", "$$"]}, {"cell_type": "markdown", "id": "f571492f", "metadata": {"tags": []}, "source": ["## 再多一点\n", "\n", "我们也可以将Notebook作为日常工作的工具，可以写各种模型、业务代码。\n", "\n", "比如，我们读取 table.txt 的数据，并对结果按创建时间排序。对这个简单任务，可以先写一个读文本的函数，再写个关于这个任务的类。"]}, {"cell_type": "code", "execution_count": 1, "id": "ac9432cf", "metadata": {}, "outputs": [], "source": ["def read_file(file_path: str, num: int = -1, sep: str = \"\\t\"):\n", "    \"\"\"\n", "    Parameters\n", "    -----------\n", "    file_path: The file path.\n", "    num: How many lines would read, default is -1, means all lines.\n", "    sep: seperator for the line.\n", "    \n", "    Return\n", "    ------\n", "    A generator of the splited item.\n", "    \"\"\"\n", "    n = 0\n", "    with open(file_path, \"r\") as f:\n", "        for i, line in enumerate(f):\n", "            if num == -1 or n < num:\n", "                yield line.strip().split(sep)\n", "            else:\n", "                break\n", "            n += 1"]}, {"cell_type": "code", "execution_count": 9, "id": "19b8508f", "metadata": {}, "outputs": [], "source": ["class Task:\n", "    \"\"\"\n", "    A Company sort task.\n", "    \"\"\"\n", "    \n", "    def __init__(self, file_path: str):\n", "        self.data = self._load_file(file_path)\n", "    \n", "    def _load_file(self, file_path: str) -> list:\n", "        res = []\n", "        lines = read_file(file_path)\n", "        for i, item in enumerate(lines):\n", "            if i == 0:\n", "                continue\n", "            company = {\n", "                \"org\": item[0],\n", "                \"est\": int(item[1]),\n", "                \"ceo\": item[2]\n", "            }\n", "            res.append(company)\n", "        return res\n", "    \n", "    def order_by(self, field: str = \"est\") -> list:\n", "        return sorted(self.data, key=lambda x: x.get(field, \"est\"))"]}, {"cell_type": "code", "execution_count": 10, "id": "4c8515ae", "metadata": {}, "outputs": [], "source": ["task = Task(\"data/table.txt\")"]}, {"cell_type": "code", "execution_count": 11, "id": "fedb8727", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'org': 'Nokia', 'est': 1865, 'ceo': '<PERSON><PERSON><PERSON>'},\n", " {'org': 'Microsoft', 'est': 1975, 'ceo': '<PERSON><PERSON><PERSON>'},\n", " {'org': 'Google', 'est': 1998, 'ceo': '<PERSON><PERSON>'}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["task.order_by()"]}, {"cell_type": "code", "execution_count": 7, "id": "759493da", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'org': 'Google', 'est': 1998, 'ceo': '<PERSON><PERSON>'},\n", " {'org': 'Microsoft', 'est': 1975, 'ceo': '<PERSON><PERSON><PERSON>'},\n", " {'org': 'Nokia', 'est': 1865, 'ceo': '<PERSON><PERSON><PERSON>'}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["task.order_by(\"org\")"]}, {"cell_type": "markdown", "id": "dff22564", "metadata": {}, "source": ["也可以导入外部的文件。"]}, {"cell_type": "code", "execution_count": 105, "id": "31ff511f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n"]}], "source": ["from demo import add"]}, {"cell_type": "code", "execution_count": 106, "id": "c378d234", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["add(3, 4)"]}, {"cell_type": "markdown", "id": "1932a75f", "metadata": {}, "source": ["## 练习\n", "\n", "- 请自行尝试本节涉及到的和相关的命令，尝试修改其中的内容。\n", "- 请将你的学习笔记（文本+代码）以Notebook记录。"]}, {"cell_type": "markdown", "id": "4294c55e", "metadata": {}, "source": ["## 参考和资料\n", "\n", "- 【1】[Built-in magic commands — IPython 8.1.1 documentation](https://ipython.readthedocs.io/en/stable/interactive/magics.html)\n", "- 【2】<PERSON>，《Pythoon 数据科学手册》，人民邮电出版社，2018年2月"]}, {"cell_type": "code", "execution_count": null, "id": "6a2854c0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 5}