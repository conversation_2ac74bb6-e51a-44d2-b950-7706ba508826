# 1.3 PyTorch相关资源

PyTorch之所以被越来越多的人使用，不仅在于其完备的教程，还受益于许多相关的资源和完善的论坛。

经过本节的学习，你将收获：

- PyTorch的优质学习资源

## 1.3.1 PyTorch学习资源
1. [Awesome-pytorch-list](https://github.com/bharathgs/Awesome-pytorch-list)：目前已获12K Star，包含了NLP,CV,常见库，论文实现以及Pytorch的其他项目。
2. [PyTorch官方文档](https://pytorch.org/docs/stable/index.html)：官方发布的文档，十分丰富。
3. [Pytorch-handbook](https://github.com/zergtant/pytorch-handbook)：GitHub上已经收获14.8K，pytorch手中书。
4. [PyTorch官方社区](https://discuss.pytorch.org/)：PyTorch拥有一个活跃的社区，在这里你可以和开发pytorch的人们进行交流。
5. [PyTorch官方tutorials](https://pytorch.org/tutorials/)：官方编写的tutorials，可以结合colab边动手边学习
6. [动手学深度学习](https://zh.d2l.ai/)：动手学深度学习是由李沐老师主讲的一门深度学习入门课，拥有成熟的书籍资源和课程资源，在B站，Youtube均有回放。
7. [Awesome-PyTorch-Chinese](https://github.com/INTERMT/Awesome-PyTorch-Chinese)：常见的中文优质PyTorch资源
8. [labml.ai Deep Learning Paper Implementations](https://github.com/labmlai/annotated_deep_learning_paper_implementations)：手把手实现经典网络代码
9. [YSDA course in Natural Language Processing](https://github.com/yandexdataschool/nlp_course):YSDA course in Natural Language Processing
10. [huggingface](https://huggingface.co/):hugging face
11. [ModelScope](https://modelscope.cn/models): 魔搭社区

除此之外，还有很多学习pytorch的资源在b站，stackoverflow，知乎......未来大家还需要多多探索，我们希望大家可以在实战中不断学习，不断给予我们课程反馈。

**以上便是第一章的内容了，能力有限，希望各位一定要带着自己的想法去思考问题，也希望各位能指出文档中的问题，我们会不断改进内容，给大家呈现一个更好的教程。**

