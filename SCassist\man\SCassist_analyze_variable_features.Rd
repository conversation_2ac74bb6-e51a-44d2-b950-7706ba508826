% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/analyze_variable_features.R
\name{SCassist_analyze_variable_features}
\alias{SCassist_analyze_variable_features}
\title{Analyze Variable Features with a Large Language Model (LLM)}
\usage{
SCassist_analyze_variable_features(llm_server = "google",
                 seurat_object_name, 
                 top_n_variable_features = 30, 
                 experimental_design = NULL, 
                 temperature = 0,
                 max_output_tokens = 10048,
                 model_G = "gemini-1.5-flash-latest",
                 model_O = "llama3",
                 model_C = "gpt-4o-mini",
                 api_key_file = "api_keys.txt",
                 model_params = list(seed = 42,temperature = 0, num_gpu = 0))
}
\arguments{
\item{llm_server}{The LLM server to use. Options are "google" or "ollama" or "openai". Default is "google".}

\item{seurat_object_name}{The name of the Seurat object containing the
single-cell RNA-seq data. The object should be accessible in the current
environment and should have either `scTransform` or `FindVariableFeatures`
already run.}

\item{top_n_variable_features}{The number of top variable features to analyze. 
Defaults to 30 if not provided.}

\item{experimental_design}{A character string describing the experimental
design. This information helps the LLM interpret the significance of
the identified gene ontologies or pathways.  If not provided, the LLM will
analyze the variable features without specific design context.}

\item{temperature}{A number between 0 and 1 controlling the creativity of the
LLM's response. Lower values produce more deterministic results. Defaults
to 0.}

\item{max_output_tokens}{The maximum number of tokens the LLM can generate 
in its response. Defaults to 10048.}

\item{model_G}{Character string specifying the Gemini model to use for
analysis. Default is "gemini-1.5-flash-latest".}

\item{model_O}{Character string specifying the Ollama model to use for
analysis. Default is "llama3".}

\item{model_C}{Character string specifying the OpenAI model to use for
analysis. Default is "gpt-4o-mini".}

\item{api_key_file}{The path to a text file containing the API key for 
accessing the LLM.}

\item{model_params}{A list of parameters to be passed to the `ollama::query` function.
This allows customization of the Llama model's behavior. Default is `list(seed = 42, temperature = 0, num_gpu = 0)`.}
}
\value{
A character string containing the LLM's analysis of the variable
 features, including a table with enriched functional categories and scores,
 and an explanation of the relevance to the experimental design (if provided).
}
\description{
This function analyzes a list of variable features from a Seurat object
using a large language model (LLM) to identify enriched gene ontologies or
pathways. The LLM considers the provided gene list and experimental design
information to provide an analysis of the functional significance of these
variable features.
}
\details{
This function was written with assistance from Google's Gemini and Meta's Llama3.
}
\examples{
\dontrun{
# Load the Seurat object
seurat_object <- readRDS("path/to/seurat_object.rds")

# Analyze variable features with Gemini
analysis_results <- SCassist_analyze_variable_features(
  seurat_object_name = "seurat_object",
  top_n_variable_features = 50,
  experimental_design = "Treatment with drug X compared to control",
  model = "gemini-1.5-flash-latest",
  api_key_file = "api_keys.txt"
)

# Print the analysis results
cat(analysis_results)
}
}
\seealso{
\code{\link{SCassist_analyze_quality}}, 
          \code{\link{SCassist_recommend_normalization}}, 
          \code{\link{SCassist_analyze_variable_features}}
}
\author{
Vijay Nagarajan, PhD, NEI/NIH
}
