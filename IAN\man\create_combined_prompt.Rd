% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_create_combined_prompt.R
\name{create_combined_prompt}
\alias{create_combined_prompt}
\title{IAN_create_combined_prompt.R}
\usage{
create_combined_prompt(
  results,
  gene_symbols,
  string_interactions,
  chea_results,
  string_network_properties,
  comparison_results,
  experimental_design = NULL
)
}
\arguments{
\item{results}{A list of results from individual agents. Each element should be a list with `agent_id` and `response` elements.}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{string_interactions}{A list containing STRING protein-protein interaction data.}

\item{chea_results}{A data frame containing ChEA transcription factor enrichment results.}

\item{string_network_properties}{A data frame containing STRING network properties.}

\item{comparison_results}{A list containing pathway comparison results.}

\item{experimental_design}{A character string describing the experimental design (optional).}
}
\value{
A character string containing the combined LLM prompt.
}
\description{
Creates a combined prompt for a Large Language Model (LLM) by integrating results from multiple agents,
STRING protein-protein interaction data, ChEA transcription factor enrichment results, STRING network properties,
pathway comparison results, and experimental design information. The prompt includes an additional step to generate a title.
}
