# 3.1 思考：完成深度学习的必要部分

经过本节的学习，你将收获：

- 一项机器学习/深度学习任务的整体流程
- 在机器学习和深度学习的每个部分的作用

一项机器学习任务时常常有以下的几个重要步骤，首先是**数据的预处理**，其中重要的步骤包括数据格式的统一、异常数据的消除和必要的数据变换，同时**划分训练集、验证集、测试集**，常见的方法包括：按比例随机选取，KFold方法（我们可以使用sklearn带的test_train_split函数、kfold来实现）。接下来**选择模型**，并设定**损失函数和优化方法**，以及对应的**超参数**（当然可以使用sklearn这样的机器学习库中模型自带的损失函数和优化器）。最后用模型去拟合训练集数据，并在**验证集/测试集上计算模型表现**。

深度学习和机器学习在流程上类似，但在代码实现上有较大的差异。首先，**由于深度学习所需的样本量很大，一次加载全部数据运行可能会超出内存容量而无法实现；同时还有批（batch）训练等提高模型表现的策略，需要每次训练读取固定数量的样本送入模型中训练**，因此深度学习在数据加载上需要有专门的设计。

在模型实现上，深度学习和机器学习也有很大差异。由于深度神经网络层数往往较多，同时会有一些用于实现特定功能的层（如卷积层、池化层、批正则化层、LSTM层等），因此**深度神经网络往往需要“逐层”搭建，或者预先定义好可以实现特定功能的模块，再把这些模块组装起来**。这种“定制化”的模型构建方式能够充分保证模型的灵活性，也对代码实现提出了新的要求。

接下来是损失函数和优化器的设定。这部分和经典机器学习的实现是类似的。但由于模型设定的灵活性，**因此损失函数和优化器要能够保证反向传播能够在用户自行定义的模型结构上实现**。

上述步骤完成后就可以开始训练了。我们前面介绍了GPU的概念和GPU用于并行计算加速的功能，不过**程序默认是在CPU上运行的**，因此在代码实现中，需要把模型和数据“放到”GPU上去做运算，同时还需要保证损失函数和优化器能够在GPU上工作。如果使用多张GPU进行训练，还需要考虑模型和数据分配、整合的问题。此外，后续计算一些指标还需要把数据“放回”CPU。这里涉及到了一系列**有关于GPU的配置和操作**。

**深度学习中训练和验证过程最大的特点在于读入数据是按批的，每次读入一个批次的数据，放入GPU中训练，然后将损失函数反向传播回网络最前面的层，同时使用优化器调整网络参数。这里会涉及到各个模块配合的问题。训练/验证后还需要根据设定好的指标计算模型表现。**

经过以上步骤，一个深度学习任务就完成了。我们在详细讲解每个部分之前，先梳理了完成各个部分所需的功能，下面我们就去进一步了解一下PyTorch是如何实现各个部分的，以及PyTorch作为一个深度学习框架拥有的模块化特点。
