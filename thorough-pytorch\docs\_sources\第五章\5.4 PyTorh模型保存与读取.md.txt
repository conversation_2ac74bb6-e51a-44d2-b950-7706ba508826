# 5.4 PyTorch模型保存与读取

在前面几节的内容中，我们介绍了如何构建和修改PyTorch模型。本节我们来讨论PyTorch如何保存和读取训练好的模型和参数。

另外，在很多场景下我们都会使用多GPU训练。这种情况下，模型会分布于各个GPU上（参加2.3节分布数据式训练，这里暂不考虑分布模型式训练），模型的保存和读取与单GPU训练情景下是否有所不同？

经过本节的学习，你将收获：

- PyTorch的模型的存储格式
- PyTorch如何存储模型
- 单卡与多卡训练下模型的保存与加载方法
- 优化器模型参数的保存


## 5.4.1 模型存储格式

PyTorch存储模型主要采用pkl，pt，pth三种格式。就使用层面来说没有区别，这里不做具体的讨论。本节最后的参考内容中列出了查阅到的一些资料，感兴趣的读者可以进一步研究，欢迎留言讨论。


## 5.4.2 模型存储内容

一个PyTorch模型主要包含两个部分：模型结构和权重。其中模型是继承nn.Module的类，权重的数据结构是一个字典（key是层名，value是权重向量）。存储也由此分为两种形式：存储整个模型（包括结构和权重），和只存储模型权重。

```python
from torchvision import models
model = models.resnet152(pretrained=True)
save_dir = './resnet152.pth'

# 保存整个模型
torch.save(model, save_dir)
# 保存模型权重
torch.save(model.state_dict, save_dir)
```

对于PyTorch而言，pt, pth和pkl**三种数据格式均支持模型权重和整个模型的存储**，因此使用上没有差别。



## 5.4.3 单卡和多卡模型存储的区别

PyTorch中将模型和数据放到GPU上有两种方式——`.cuda()`和`.to(device)`，本节后续内容针对前一种方式进行讨论。如果要使用多卡训练的话，需要对模型使用`torch.nn.DataParallel`。示例如下：

```python
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0' # 如果是多卡改成类似0,1,2
model = model.cuda()  # 单卡
model = torch.nn.DataParallel(model).cuda()  # 多卡
```

之后我们把`model`对应的`layer`名称打印出来看一下，可以观察到差别在于多卡并行的模型每层的名称前多了一个“module”。

- 单卡模型的层名：

![img](https://pic3.zhimg.com/v2-3490f6ab8bc806274dd017e1a66e2486_b.png)

- 多卡模型的层名：

![img](https://pic3.zhimg.com/v2-4b611c24c2e702749cebbe65eaff7cde_b.png)

这种模型表示的不同可能会导致模型保存和加载过程中需要处理一些矛盾点，下面对各种可能的情况做分类讨论。


## 5.4.4 单卡/多卡情况分类讨论

由于训练和测试所使用的硬件条件不同，在模型的保存和加载过程中可能因为单GPU和多GPU环境的不同带来模型不匹配等问题。这里对PyTorch框架下单卡/多卡下模型的保存和加载问题进行排列组合（=4），样例模型是torchvision中预训练模型resnet152，不尽之处欢迎大家补充。

- **单卡保存+单卡加载**

在使用os.envision命令指定使用的GPU后，即可进行模型保存和读取操作。注意这里即便保存和读取时使用的GPU不同也无妨。

```python
import os
import torch
from torchvision import models

os.environ['CUDA_VISIBLE_DEVICES'] = '0'   #这里替换成希望使用的GPU编号
model = models.resnet152(pretrained=True)
model.cuda()

save_dir = 'resnet152.pt'   #保存路径

# 保存+读取整个模型
torch.save(model, save_dir)
loaded_model = torch.load(save_dir)
loaded_model.cuda()

# 保存+读取模型权重
torch.save(model.state_dict(), save_dir)
loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.load_state_dict(torch.load(save_dir))
loaded_model.cuda()
```

- **单卡保存+多卡加载**

这种情况的处理比较简单，读取单卡保存的模型后，使用`nn.DataParallel`函数进行分布式训练设置即可（相当于3.1代码中.cuda()替换一下）：

```python
import os
import torch
from torchvision import models

os.environ['CUDA_VISIBLE_DEVICES'] = '0'   #这里替换成希望使用的GPU编号
model = models.resnet152(pretrained=True)
model.cuda()

# 保存+读取整个模型
torch.save(model, save_dir)

os.environ['CUDA_VISIBLE_DEVICES'] = '1,2'   #这里替换成希望使用的GPU编号
loaded_model = torch.load(save_dir)
loaded_model = nn.DataParallel(loaded_model).cuda()

# 保存+读取模型权重
torch.save(model.state_dict(), save_dir)

os.environ['CUDA_VISIBLE_DEVICES'] = '1,2'   #这里替换成希望使用的GPU编号
loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.load_state_dict(torch.load(save_dir))
loaded_model = nn.DataParallel(loaded_model).cuda()
```

- **多卡保存+单卡加载**

这种情况下的核心问题是：如何去掉权重字典键名中的"module"，以保证模型的统一性。

对于加载整个模型，直接提取模型的module属性即可：

```python
import os
import torch
from torchvision import models

os.environ['CUDA_VISIBLE_DEVICES'] = '1,2'   #这里替换成希望使用的GPU编号

model = models.resnet152(pretrained=True)
model = nn.DataParallel(model).cuda()

# 保存+读取整个模型
torch.save(model, save_dir)

os.environ['CUDA_VISIBLE_DEVICES'] = '0'   #这里替换成希望使用的GPU编号
loaded_model = torch.load(save_dir).module
```

对于加载模型权重，有以下几种思路：
**保存模型时保存模型的module属性对应的权重**
```python
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2'   #这里替换成希望使用的GPU编号
import torch
from torchvision import models

save_dir = 'resnet152.pth'   #保存路径
model = models.resnet152(pretrained=True)
model = nn.DataParallel(model).cuda()

# 保存权重
torch.save(model.module.state_dict(), save_dir)
```
这样保存下来的模型参数就和单卡保存的模型参数一样了，可以直接加载。也是比较推荐的一种方法。
**去除字典里的module麻烦，往model里添加module简单**

```python
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2'   #这里替换成希望使用的GPU编号
import torch
from torchvision import models

model = models.resnet152(pretrained=True)
model = nn.DataParallel(model).cuda()

# 保存+读取模型权重
torch.save(model.state_dict(), save_dir)

os.environ['CUDA_VISIBLE_DEVICES'] = '0'   #这里替换成希望使用的GPU编号
loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.load_state_dict(torch.load(save_dir))
loaded_model = nn.DataParallel(loaded_model).cuda()
loaded_model.state_dict = loaded_dict
```

这样即便是单卡，也可以开始训练了（相当于分布到单卡上）

**遍历字典去除module**

```python
from collections import OrderedDict
os.environ['CUDA_VISIBLE_DEVICES'] = '0'   #这里替换成希望使用的GPU编号

loaded_dict = torch.load(save_dir)

new_state_dict = OrderedDict()
for k, v in loaded_dict.items():
    name = k[7:] # module字段在最前面，从第7个字符开始就可以去掉module
    new_state_dict[name] = v #新字典的key值对应的value一一对应

loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.state_dict = new_state_dict
loaded_model = loaded_model.cuda()
```

**使用replace操作去除module**

```python
loaded_model = models.resnet152()    
loaded_dict = torch.load(save_dir)
loaded_model.load_state_dict({k.replace('module.', ''): v for k, v in loaded_dict.items()})
```



- **多卡保存+多卡加载**

由于是模型保存和加载都使用的是多卡，因此不存在模型层名前缀不同的问题。但多卡状态下存在一个device（使用的GPU）匹配的问题，即**保存整个模型**时会同时保存所使用的GPU id等信息，读取时若这些信息和当前使用的GPU信息不符则可能会报错或者程序不按预定状态运行。具体表现为以下两点：

**读取整个模型再使用nn.DataParallel进行分布式训练设置**

这种情况很可能会造成保存的整个模型中GPU id和读取环境下设置的GPU id不符，训练时数据所在device和模型所在device不一致而报错。

**读取整个模型而不使用nn.DataParallel进行分布式训练设置**

这种情况可能不会报错，测试中发现程序会自动使用设备的前n个GPU进行训练（n是保存的模型使用的GPU个数）。此时如果指定的GPU个数少于n，则会报错。在这种情况下，只有保存模型时环境的device id和读取模型时环境的device id一致，程序才会按照预期在指定的GPU上进行分布式训练。

相比之下，读取模型权重，之后再使用nn.DataParallel进行分布式训练设置则没有问题。因此**多卡模式下建议使用权重的方式存储和读取模型**：

```python
import os
import torch
from torchvision import models

os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2'   #这里替换成希望使用的GPU编号

model = models.resnet152(pretrained=True)
model = nn.DataParallel(model).cuda()

# 保存+读取模型权重，强烈建议！！
torch.save(model.state_dict(), save_dir)
loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.load_state_dict(torch.load(save_dir)))
loaded_model = nn.DataParallel(loaded_model).cuda()
```

如果只有保存的整个模型，也可以采用提取权重的方式构建新的模型：

```python
# 读取整个模型
loaded_whole_model = torch.load(save_dir)
loaded_model = models.resnet152()   #注意这里需要对模型结构有定义
loaded_model.state_dict = loaded_whole_model.state_dict
loaded_model = nn.DataParallel(loaded_model).cuda()
```

另外，上面所有对于loaded_model修改权重字典的形式都是通过赋值来实现的，在PyTorch中还可以通过"load_state_dict"函数来实现。因此在上面的所有示例中，我们使用了两种实现方式。

```python
loaded_model.load_state_dict(loaded_dict)
```

## 5.4.5 其他参数的保存和读取
在深度学习项目里，有时候我们不仅仅需要保存模型的权重，还需要保存一些其他的参数，比如训练的epoch数、训练的loss，优化器的参数，动态调整学习策略的参数等等。这些参数可以通过字典的形式保存在一个文件里，然后在读取模型时一起读取。这里我们以下方代码为例：
```python
torch.save({
        'model': model.state_dict(),
        'optimizer': optimizer.state_dict(),
        'lr_scheduler': lr_scheduler.state_dict(),
        'epoch': epoch,
        'args': args,
    }, checkpoint_path)
```
这些参数的读取方式也是类似的：
```python
checkpoint = torch.load(checkpoint_path)
model.load_state_dict(checkpoint['model'])
optimizer.load_state_dict(checkpoint['optimizer'])
lr_scheduler.load_state_dict(checkpoint['lr_scheduler'])
epoch = checkpoint['epoch']
args = checkpoint['args']
```

## 附：测试环境

OS: Ubuntu 20.04 LTS GPU: GeForce RTX 2080 Ti (x3)



## 参考资料

本章内容同时发布于[知乎](https://zhuanlan.zhihu.com/p/371090724)和[CSDN](https://blog.csdn.net/goodljq/article/details/117258032)

1. [pytorch 中pkl和pth的区别？](https://www.zhihu.com/question/274533811)  
2. [What is the difference between .pt, .pth and .pwf extentions in PyTorch?](https://stackoverflow.com/questions/59095824/what-is-the-difference-between-pt-pth-and-pwf-extentions-in-pytorch)
