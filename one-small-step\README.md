One Small Step 
--------------

by @karminski-牙医

![one-small-step](assets/images/Apollo_11_mission_Buzz_Aldrins_boot_on_lunar_soil_2017_Bing_Wallpaper_1366x768.jpg)

**这是一个简单的技术科普教程项目, 主要聚焦于解释一些有趣的, 前沿的技术概念和原理. 每篇文章都力求在 5 分钟内阅读完成.**  


![Metrics](./assets/images/metrics-without-label.svg)  

**目前更新速度👆, 力求每周不低于3篇**

## 文章列表

### 人工智能相关
- [什么是 GGUF](20250113-what-is-gguf/what-is-gguf.md) - 介绍 GGUF 文件格式及其在大语言模型部署中的应用
- [什么是推测性解码](20250116-what-is-speculative-decoding/what-is-speculative-decoding.md) - 解释推测性解码技术如何提升大语言模型的推理性能
- [什么是 Pythonic 函数调用](20250117-what-is-pythonic-function-call/what-is-pythonic-function-call.md) - 为什么 Pythonic 函数调用 比 function call 效果好?
- [如何本地运行 GGUF 格式的 LLM 模型](20250122-how-to-run-gguf-LLM-model/how-to-run-gguf-LLM-model.md) - 如何本地运行 GGUF 格式的 LLM 模型?
- [什么是 LLM 蒸馏技术](20250123-what-is-LLM-distill/what-is-LLM-distill.md) - 什么是 LLM 蒸馏技术?
- [什么是 Transformer](20250126-what-is-transformer/what-is-transformer.md) - 什么是 Transformer?
- [如何优化 Transformer](20250127-how-to-optimize-transformer/how-to-optimize-transformer.md) - Transformer 的优化方案都有哪些?
- [什么是大语言模型量化](20250129-what-is-quantization-in-LLM/what-is-quantization-in-LLM.md) - 什么是大语言模型量化? 每个量化精度都代表什么?
- [什么是 Flash Attention](20250201-what-is-flash-attention/what-is-flash-attention.md) - 什么是 Flash Attention? 为什么能将大语言模型推理速度提升3倍?
- [什么是 Multi-Head Attention](20250202-what-is-multi-head-attention/what-is-multi-head-attention.md) - 什么是 Multi-Head Attention? Attention Is All You Need 论文精读
- [什么是 Multi-Query Attention](20250204-what-is-multi-query-attention/what-is-multi-query-attention.md) - 什么是 Multi-Query Attention?
- [什么是 Grouped Query Attention](20250205-what-is-gropued-query-attention/what-is-gropued-query-attention.md) - 什么是 Grouped Query Attention?
- [什么是 LLM 微调技术](20250208-what-is-LLM-fine-tuning/what-is-LLM-fine-tuning.md) - 什么是 LLM 微调技术?
- [什么是 RAG 技术](20250209-what-is-RAG/what-is-RAG.md) - 什么是 RAG 技术?
- [什么是 Safetensors](20250210-what-is-safetensors/what-is-safetensors.md) - 什么是 Safetensors?
- [什么是 ONNX](20250211-what-is-onnx/what-is-onnx.md) - 什么是 ONNX?
- [大模型微调最佳实践指南](20250210-LLM-fine-tuning-summary/LLM-fine-tuning-summary.md) - 大模型微调最佳实践指南
- [什么是 MoE 模型](20250217-what-is-MoE/what-is-MoE-model.md) - 什么是 MoE 模型?
- [LLM 中的 Token 是如何计算的](20250218-how-are-tokens-calculated-in-LLMs/how-are-tokens-calculated-in-LLMs.md) - LLM 中的 Token 是如何计算的?
- [什么是 AI Agent](20250220-what-is-AI-Agent/what-is-AI-Agent.md) - 什么是 AI Agent?
- [什么是 LoRA](20250228-what-is-LoRA/what-is-LoRA.md) - 什么是 LoRA? 为什么用 LoRA 微调大模型更高效?
- [什么是向量嵌入](20250307-what-is-vector-embedding/what-is-vector-embedding.md) - 什么是向量嵌入? 
- [什么是向量数据库](20250308-what-is-vector-database/what-is-vector-database.md) - 什么是向量数据库?
- [什么是 AI 幻觉](20250309-what-is-AI-Hallucination/what-is-AI-Hallucination.md) - 什么是 AI 幻觉?
- [什么是模态编码](20250315-what-is-modal-encoding/what-is-modal-encoding.md) - 什么是模态编码?
- [什么是表示空间](20250316-what-is-representation-space/what-is-representation-space.md) - 什么是表示空间?
- [什么是多模态模型](20250317-what-is-multi-model-llm/what-is-multi-model-llm.md) - 什么是多模态模型?
- [什么是 LLM 的困惑度](20250403-what-is-llm-perplexity/what-is-llm-perplexity.md) - 什么是大模型的困惑度? 为什么量化版本要用困惑度来评价量化质量?
- [如何避免 KVCache 失效](20250513-How-to-avoid-KVCache-invalidation/How-to-avoid-KVCache-invalidation.md) - 当心动态内容, 会导致 KVCache 失效
- [什么是 Sliding Window Attention](20250522-what-is-sliding-window-attention/what-is-sliding-window-attention.md) - 什么是滑动窗口注意力
- [什么时候应该微调, 什么时候不应该微调?](20250530-When-to-Use-Fine-Tuning-and-When-Not-To/When-to-Use-Fine-Tuning-and-When-Not-To.md)

### 数学相关
- [什么是矩阵的秩？什么是低秩矩阵？](20250227-what-is-rank-in-matrix/what-is-rank-in-matrix.md) - 什么是矩阵的秩？什么是低秩矩阵？
- [什么是拟合与过拟合](20250301-what-is-fitting-and-overfitting/what-is-fitting-and-overfitting.md) - 什么是拟合与过拟合?

### 系统相关
- [Windows 任务管理器内存标签说明](20250104-windows-task-manager-memory-tab-description/windows-task-manager-memory-tab-description.md) - 详解 Windows 任务管理器中各个内存指标的含义
- [RAMMap 使用解析](20250128-rammap-description/rammap-description.md) - 详解 RAMMap 的使用方法

### 硬件相关
- [什么是 PCIe Retimer](20250119-what-is-pcie-retimer/what-is-pcie-retimer.md) - 详解 PCIe Retimer 的原理和应用
- [为什么有的 NVMe SSD 有 DRAM, 有的没有?](20250124-why-some-NVMe-SSD-have-DRAM-and-some-are-not/why-some-NVMe-SSD-have-DRAM-and-some-are-not.md) - 为什么有的 NVMe SSD 有 DRAM, 有的没有?
- [CLX 会是大语言模型的内存解决方案吗?](20250125-does-CXL-will-be-LLM-memory-solution/does-CXL-will-be-LLM-memory-solution.md) - 什么? PCIe 上能插内存了?
- [什么是 1DPC](20250131-what-is-1DPC/what-is-1DPC.md) - 什么是 1DPC? 为什么内存条要插在远端插槽?
- [什么是 L1 缓存](20250206-what-is-L1-cache/what-is-L1-cache.md) - 什么是 L1 缓存? 它的工作原理是什么?
  
## 贡献

由于个人能力有限, 难免会有错误, 欢迎大家指正, 任何形式的贡献或者讨论都十分欢迎, 可以提交 issue 或者直接 PR.

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=karminski/one-small-step&type=Date)](https://star-history.com/#karminski/one-small-step&Date)


## 许可

本项目采用 MIT 许可证. 详见 [LICENSE](LICENSE) 文件. 
