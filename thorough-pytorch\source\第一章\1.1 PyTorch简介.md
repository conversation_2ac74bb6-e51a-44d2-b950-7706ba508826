# 1.1 PyTorch简介

PyTorch是由Meta AI(Facebook)人工智能研究小组开发的一种基于Lua编写的Torch库的Python实现的深度学习库，目前被广泛应用于学术界和工业界，相较于Tensorflow2.x，PyTorch在API的设计上更加简洁、优雅和易懂。因此本课程我们选择PyTorch来进行开源学习。

经过本节的学习，你将收获：

- 了解PyTorch的发展流程
- 了解PyTorch相较于其他框架的优势


## 1.1.1 PyTorch的发展

**“All in PyTorch”**，对于PyTorch的发展我们只能用一句话来概况了，PyTorch自从推出就获得巨大的关注并受到了很多人的喜欢，而最直观的莫过于下面数据所表现的简明直了。

下图来自[Paper with code](https://paperswithcode.com/trends)网站，**颜色面积代表使用该框架的论文公开代码库的数量**，我们可以发现截至2021年6月，PyTorch的代码实现已经是TensorFlow实现的4倍，我们也可以看红色部分的PyTorch正在取代他的老大哥称霸学术圈，PyTorch会借助ONNX所带来的落地能力在工业界逐渐走向主导地位。

总的来说，我们必须承认到现在为止PyTorch 1.x还是有不如别的框架的地方，但是我们相信PyTorch 2.x版本会给我们带来更大的惊喜。

![框架对比图](figures/main_compare.png)

## 1.1.2 PyTorch的优势
+ **更加简洁**，相比于其他的框架，PyTorch的框架更加简洁，易于理解。PyTorch的设计追求最少的封装，避免重复造轮子。
+ **上手快**，掌握numpy和基本的深度学习知识就可以上手。
+ PyTorch有着**良好的文档和社区支持**，作者亲自维护的论坛供用户交流和求教问题。Meta AI(Facebook AI)对PyTorch提供了强力支持，作为当今排名前三的深度学习研究机构，MAIR的支持足以确保PyTorch获得持续的开发更新。
+ **项目开源**，在Github上有越来越多的开源代码是使用PyTorch进行开发。
+ 可以**更好的调试代码**，PyTorch可以让我们逐行执行我们的脚本。这就像调试NumPy一样 – 我们可以轻松访问代码中的所有对象，并且可以使用打印语句（或其他标准的Python调试）来查看方法失败的位置。
+ 越来越完善的扩展库，活力旺盛，正处在**当打之年**。

