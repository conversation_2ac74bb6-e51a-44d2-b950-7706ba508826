name: Metrics Generation
on:
  workflow_dispatch:        # 允许手动触发
  push:
    branches:
      - main              # 当 main 分支有推送时运行

jobs:
  metrics:
    runs-on: ubuntu-latest
    permissions:
      contents: write     # 需要写入权限来更新 README
    steps:
      - uses: actions/checkout@v3    # 首先需要检出代码
      
      - name: Generate Metrics
        uses: lowlighter/metrics@latest
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          base: ""        # 不包含基础指标
          template: classic
          config_timezone: Asia/Shanghai  # 设置时区为中国
          repositories_forks: no
          repositories: "karminski/one-small-step"  # 指定你的仓库
          plugin_calendar: yes
          filename: assets/images/metrics.svg          # 输出文件名
          
      - name: Remove h2 tags and adjust height in SVG
        run: |
          perl -i -p0e 's/<h2[^>]*>.*?<\/h2>//gs' assets/images/metrics.svg
          sed -i '1s/height="[0-9]*"/height="80"/' assets/images/metrics.svg
          cp assets/images/metrics.svg assets/images/metrics-without-label.svg
          
      - name: Commit changes
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add assets/images/metrics.svg assets/images/metrics-without-label.svg
          git stash
          git pull --rebase
          git stash pop
          git add assets/images/metrics.svg assets/images/metrics-without-label.svg
          git commit -m "chore: update metrics svg files" || echo "No changes to commit"
          git push

  actions-timeline:
    needs: [metrics]
    runs-on: ubuntu-latest
    steps:
    - uses: Kesin11/actions-timeline@v2
