% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_multi_agent_system.R
\name{Environment}
\alias{Environment}
\title{Environment Class}
\description{
Environment Class

Environment Class
}
\details{
Represents the environment in which the agents operate.
}
\section{Methods}{
\subsection{Public methods}{
\itemize{
\item \href{#method-Environment-new}{\code{Environment$new()}}
\item \href{#method-Environment-run_agents}{\code{Environment$run_agents()}}
\item \href{#method-Environment-clone}{\code{Environment$clone()}}
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Environment-new"></a>}}
\if{latex}{\out{\hypertarget{method-Environment-new}{}}}
\subsection{Method \code{new()}}{
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Environment$new(prompts, prompt_types)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{prompts}}{A list of prompts for the agents.}

\item{\code{prompt_types}}{A list of prompt types for the agents.}
}
\if{html}{\out{</div>}}
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Environment-run_agents"></a>}}
\if{latex}{\out{\hypertarget{method-Environment-run_agents}{}}}
\subsection{Method \code{run_agents()}}{
Runs the agents in the environment.
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Environment$run_agents(
  make_gemini_request_func,
  temperature,
  max_output_tokens,
  api_key,
  model_query,
  delay_seconds,
  num_workers,
  max_retries = 3
)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{make_gemini_request_func}}{The function to make the Gemini API request.}

\item{\code{temperature}}{Numeric value controlling the randomness of the response.}

\item{\code{max_output_tokens}}{Integer specifying the maximum number of tokens in the response.}

\item{\code{api_key}}{Character string containing the Gemini API key.}

\item{\code{model_query}}{Character string specifying the model to query.}

\item{\code{delay_seconds}}{Numeric value specifying the delay in seconds after sending the request.}

\item{\code{num_workers}}{Integer specifying the number of workers to use for parallel execution.}

\item{\code{max_retries}}{Integer specifying the maximum number of retries for each agent.}
}
\if{html}{\out{</div>}}
}
\subsection{Returns}{
A list of results from the agents.
}
}
\if{html}{\out{<hr>}}
\if{html}{\out{<a id="method-Environment-clone"></a>}}
\if{latex}{\out{\hypertarget{method-Environment-clone}{}}}
\subsection{Method \code{clone()}}{
The objects of this class are cloneable with this method.
\subsection{Usage}{
\if{html}{\out{<div class="r">}}\preformatted{Environment$clone(deep = FALSE)}\if{html}{\out{</div>}}
}

\subsection{Arguments}{
\if{html}{\out{<div class="arguments">}}
\describe{
\item{\code{deep}}{Whether to make a deep clone.}
}
\if{html}{\out{</div>}}
}
}
}
