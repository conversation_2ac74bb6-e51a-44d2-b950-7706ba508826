# Generated by roxygen2: do not edit by hand

export(SCassist_analyze_and_annotate)
export(SCassist_analyze_enrichment)
export(SCassist_analyze_pcs)
export(SCassist_analyze_quality)
export(SCassist_analyze_variable_features)
export(SCassist_recommend_k)
export(SCassist_recommend_normalization)
export(SCassist_recommend_pcs)
export(SCassist_recommend_res)
import(BiocManager)
import(httr)
import(jsonlite)
import(org.Hs.eg.db)
import(rollama)
import(visNetwork)
importFrom(Seurat,DefaultAssay)
importFrom(Seurat,Idents)
importFrom(Seurat,VariableFeatures)
importFrom(clusterProfiler,bitr)
importFrom(clusterProfiler,enrichGO)
importFrom(clusterProfiler,enrichKEGG)
importFrom(data.table,as.data.table)
importFrom(dplyr,"%>%")
importFrom(httr,POST)
importFrom(httr,content)
importFrom(httr,content_type_json)
importFrom(jsonlite,fromJSON)
importFrom(plyr,mapvalues)
importFrom(stats,median)
importFrom(stats,quantile)
importFrom(stats,sd)
importFrom(stats,var)
importFrom(utils,capture.output)
importFrom(utils,head)
importFrom(utils,write.table)
