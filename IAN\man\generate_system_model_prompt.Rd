% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_system_model_prompt_generator.R
\name{generate_system_model_prompt}
\alias{generate_system_model_prompt}
\title{IAN_system_model_prompt_generator.R}
\usage{
generate_system_model_prompt(
  llm_response,
  gene_symbols,
  experimental_design = NULL
)
}
\arguments{
\item{llm_response}{Character string containing the LLM response with the system network in TSV format.}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{experimental_design}{A character string describing the experimental design (optional).}
}
\value{
A character string containing the LLM prompt, or NULL if no valid TSV block is found in the LLM response.
}
\description{
Generates a prompt for a Large Language Model (LLM) to analyze a system representation network
and create a mechanistic model, including a summary, potential upstream regulators, and a hypothesis
about the molecular mechanisms contributing to the phenotype.
}
