name: Happy-LLM 问题反馈 / Textbook Issue Report
description: 报告 Happy-LLM 中的代码/内容问题 / Report code/content issues in textbook
title: "[问题/Issue] 章节X.X：简短问题描述 / ChapterX.X: Brief description"
labels: ["documentation", "需要审核/needs-review"]
body:
  - type: markdown
    attributes:
      value: |
        🐛 **请提供以下信息帮助我们快速定位问题 / Please provide the following information to help us locate the issue**  

  - type: input
    id: chapter
    attributes:
      label: 1. 遇到问题的章节 / Affected Chapter
      description: "格式示例：Chapter3.2 或 Chapter6.4 / Format example: Chapter3.2 or Chapter6.4"
      placeholder: "ChapterX.X"
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: 2. 具体问题描述 / Problem Description
      description: |
        请清晰描述遇到的问题现象或错误信息 / Clearly describe the issue or error message
        - 代码问题：遇到了什么错误？预期行为是什么？/ Code issue: What error occurred? Expected behavior?
        - 内容问题：哪里不准确/有歧义？/ Content issue: What is inaccurate/ambiguous?
      placeholder: |
        例：代码运行时出现XX错误 / E.g.: XX error when running code...
        例：图3-5的内容描述不准确 / E.g.: Inaccurate description in Figure 3-5...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: 3. 问题重现材料 / Reproduction Materials
      description: |
        🔧 根据问题类型提供以下内容 / Provide based on issue type:
        - 代码问题：完整代码片段+错误日志 / Code issue: Full code snippet + error log
        - 内容问题：相关段落/图片编号+修改建议 / Content issue: Related paragraph/figure + suggested fix
        - 格式问题：截图+问题位置描述 / Format issue: Screenshot + location description
      placeholder: |
        ```python
        # 代码问题请粘贴完整代码 / Paste full code for code issues
        print("问题重现代码 / reproduction code")
        ```
        
        **错误信息 / Error log**:
        ```
        粘贴完整的错误日志 / Paste full error log
        ```
        
        **建议修改 / Suggested fix**:
        对原内容的修改建议... / Your suggested modification...
    validations:
      required: true

  - type: checkboxes
    id: checks
    attributes:
      label: 确认事项 / Verification
      description: 请确认以下事项 / Please confirm the following
      options:
        - label: 此问题未在过往Issue中被报告过 / This issue hasn't been reported before
          required: true