# 8.1 本章简介

恭喜你，经过前面七章内容的学习，你已经逐步熟悉了PyTorch的使用，能够定义和修改自己的模型，学会了常用的训练技巧，并通过可视化辅助PyTorch的使用。

PyTorch的强大并不仅局限于自身的易用性，更在于开源社区围绕PyTorch所产生的一系列工具包（一般是Python package）和程序，这些优秀的工具包极大地方便了PyTorch在特定领域的使用。比如对于计算机视觉，有TorchVision、TorchVideo等用于图片和视频处理；对于自然语言处理，有torchtext；对于图卷积网络，有PyTorch Geometric ······。这里只是举例，每个领域还有很多优秀的工具包供社区使用。这些工具包共同构成了PyTorch的生态（EcoSystem）。

PyTorch生态很大程度助力了PyTorch的推广与成功。在特定领域使用PyTorch生态中的工具包，能够极大地降低入门门槛，方便复现已有的工作。比如我们在讨论模型修改时候就用到了torchvision中预定义的resnet结构，而不需要自己重新编写。同时，PyTorch生态有助于社区力量的加入，共同为社区提供更有价值的内容和程序，这也是开源理念所坚持的价值。

在后面的内容中，我们会逐步介绍PyTorch生态在图像、视频、文本等领域中的发展，针对某个领域我们选择其中有代表性的一个工具包进行详细介绍，主要包括工具包的作者或其所在机构、数据预处理工具（这块可能再引入第三方工具包）、数据扩增、常用模型结构的预定义、预训练模型权重、常用损失函数、常用评测指标、封装好的训练&测试模块，以及可视化工具。这些内容也是我们在使用对应工具包时会用到的。读者可以根据自身需要重点学习，对于自己研究所不涉及的工具包，可以只做了解，需要使用时再来学习。



**注：**

本章内容会不断更新，欢迎大家在issue中提出宝贵建议，或者直接pull request~

