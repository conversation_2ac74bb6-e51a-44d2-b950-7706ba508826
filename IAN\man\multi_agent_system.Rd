% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_multi_agent_system.R
\name{multi_agent_system}
\alias{multi_agent_system}
\alias{make_gemini_request}
\title{IAN_multi_agent_system.R}
\usage{
make_gemini_request(
  prompt,
  temperature,
  max_output_tokens,
  api_key,
  model_query,
  delay_seconds
)
}
\arguments{
\item{prompt}{Character string containing the prompt to send to the Gemini API.}

\item{temperature}{Numeric value controlling the randomness of the response.}

\item{max_output_tokens}{Integer specifying the maximum number of tokens in the response.}

\item{api_key}{Character string containing the Gemini API key.}

\item{model_query}{Character string specifying the model to query.}

\item{delay_seconds}{Numeric value specifying the delay in seconds after sending the request.}
}
\value{
Character string containing the response from the Gemini API, or a list with an "error" element if the request fails.
}
\description{
This script defines a multi-agent system where agents interact with the Gemini API to generate responses based on predefined prompts.
It includes functions for making API requests, defining agent behavior, and managing the environment in which agents operate.
}
\section{Functions}{

\describe{
  \item{\code{\link{make_gemini_request}}}{: Sends a request to the Gemini API with a given prompt and configuration.}
}
}

\section{Classes}{

\describe{
  \item{\code{\link{Agent}}}{: Represents an agent in the multi-agent system.}
  \item{\code{\link{Environment}}}{: Represents the environment in which the agents operate.}
}
}

