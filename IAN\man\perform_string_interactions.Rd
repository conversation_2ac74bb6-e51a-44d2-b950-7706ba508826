% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_enrichment_analysis.R
\name{perform_string_interactions}
\alias{perform_string_interactions}
\title{Perform STRING Interaction Analysis}
\usage{
perform_string_interactions(
  gene_mapping,
  organism,
  score_threshold = 0,
  output_dir = "enrichment_results"
)
}
\arguments{
\item{gene_mapping}{A data frame containing gene mappings between SYMBOL and STRING_id. Must have columns named "SYMBOL" and "STRING_id".}

\item{organism}{Character string specifying the organism. Must be "human" or "mouse".}

\item{score_threshold}{Numeric value specifying the minimum combined score for interactions. Default is 0.}

\item{output_dir}{Character string specifying the directory to save the results to. Default is "enrichment_results".}
}
\value{
A list containing two data frames: `interactions` (filtered STRING interactions) and `network_properties` (network properties of the genes).  Returns a list with an "error" element if the analysis fails.
}
\description{
Retrieves protein-protein interactions from the STRING database and performs network analysis.
}
