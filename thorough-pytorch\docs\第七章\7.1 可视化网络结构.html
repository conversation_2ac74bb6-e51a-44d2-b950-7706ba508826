
<!DOCTYPE html>

<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

    <title>7.1 可视化网络结构 &#8212; 深入浅出PyTorch</title>
    
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">
<link href="../_static/styles/pydata-sphinx-theme.css?digest=1999514e3f237ded88cf" rel="stylesheet">

    
  <link rel="stylesheet"
    href="../_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" href="../_static/styles/sphinx-book-theme.css?digest=62ba249389abaaa9ffc34bf36a076bdc1d65ee18" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../_static/togglebutton.css" />
    <link rel="stylesheet" type="text/css" href="../_static/mystnb.css" />
    <link rel="stylesheet" type="text/css" href="../_static/plot_directive.css" />
    
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf">

    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/doctools.js"></script>
    <script>let toggleHintShow = 'Click to show';</script>
    <script>let toggleHintHide = 'Click to hide';</script>
    <script>let toggleOpenOnPrint = 'true';</script>
    <script src="../_static/togglebutton.js"></script>
    <script src="../_static/scripts/sphinx-book-theme.js?digest=f31d14ad54b65d19161ba51d4ffff3a77ae00456"></script>
    <script>var togglebuttonSelector = '.toggle, .admonition.dropdown, .tag_hide_input div.cell_input, .tag_hide-input div.cell_input, .tag_hide_output div.cell_output, .tag_hide-output div.cell_output, .tag_hide_cell.cell, .tag_hide-cell.cell';</script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="7.2 CNN可视化" href="7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html" />
    <link rel="prev" title="第七章：PyTorch可视化" href="index.html" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="docsearch:language" content="zh">
    

    <!-- Google Analytics -->
    
  </head>
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="60">
<!-- Checkboxes to toggle the left sidebar -->
<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation" aria-label="Toggle navigation sidebar">
<label class="overlay overlay-navbar" for="__navigation">
    <div class="visually-hidden">Toggle navigation sidebar</div>
</label>
<!-- Checkboxes to toggle the in-page toc -->
<input type="checkbox" class="sidebar-toggle" name="__page-toc" id="__page-toc" aria-label="Toggle in-page Table of Contents">
<label class="overlay overlay-pagetoc" for="__page-toc">
    <div class="visually-hidden">Toggle in-page Table of Contents</div>
</label>
<!-- Headers at the top -->
<div class="announcement header-item noprint"></div>
<div class="header header-item noprint"></div>

    
    <div class="container-fluid" id="banner"></div>

    

    <div class="container-xl">
      <div class="row">
          
<!-- Sidebar -->
<div class="bd-sidebar noprint" id="site-navigation">
    <div class="bd-sidebar__content">
        <div class="bd-sidebar__top"><div class="navbar-brand-box">
    <a class="navbar-brand text-wrap" href="../index.html">
      
      
      
      <h1 class="site-logo" id="site-title">深入浅出PyTorch</h1>
      
    </a>
</div><form class="bd-search d-flex align-items-center" action="../search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main">
    <div class="bd-toc-item active">
        <p aria-level="2" class="caption" role="heading">
 <span class="caption-text">
  目录
 </span>
</p>
<ul class="current nav bd-sidenav">
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/index.html">
   第零章：前置知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-1" name="toctree-checkbox-1" type="checkbox"/>
  <label for="toctree-checkbox-1">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.1%20%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%AE%80%E5%8F%B2.html">
     人工智能简史
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.2%20%E8%AF%84%E4%BB%B7%E6%8C%87%E6%A0%87.html">
     模型评价指标
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.3%20%E5%B8%B8%E7%94%A8%E5%8C%85%E7%9A%84%E5%AD%A6%E4%B9%A0.html">
     常用包的学习
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E9%9B%B6%E7%AB%A0/0.4%20Jupyter%E7%9B%B8%E5%85%B3%E6%93%8D%E4%BD%9C.html">
     Jupyter notebook/Lab 简述
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/index.html">
   第一章：PyTorch的简介和安装
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-2" name="toctree-checkbox-2" type="checkbox"/>
  <label for="toctree-checkbox-2">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.1%20PyTorch%E7%AE%80%E4%BB%8B.html">
     1.1 PyTorch简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.2%20PyTorch%E7%9A%84%E5%AE%89%E8%A3%85.html">
     1.2 PyTorch的安装
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%80%E7%AB%A0/1.3%20PyTorch%E7%9B%B8%E5%85%B3%E8%B5%84%E6%BA%90.html">
     1.3 PyTorch相关资源
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/index.html">
   第二章：PyTorch基础知识
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-3" name="toctree-checkbox-3" type="checkbox"/>
  <label for="toctree-checkbox-3">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.1%20%E5%BC%A0%E9%87%8F.html">
     2.1 张量
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.2%20%E8%87%AA%E5%8A%A8%E6%B1%82%E5%AF%BC.html">
     2.2 自动求导
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.3%20%E5%B9%B6%E8%A1%8C%E8%AE%A1%E7%AE%97%E7%AE%80%E4%BB%8B.html">
     2.3 并行计算简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%8C%E7%AB%A0/2.4%20AI%E7%A1%AC%E4%BB%B6%E5%8A%A0%E9%80%9F%E8%AE%BE%E5%A4%87.html">
     AI硬件加速设备
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/index.html">
   第三章：PyTorch的主要组成模块
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-4" name="toctree-checkbox-4" type="checkbox"/>
  <label for="toctree-checkbox-4">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.1%20%E6%80%9D%E8%80%83%EF%BC%9A%E5%AE%8C%E6%88%90%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E7%9A%84%E5%BF%85%E8%A6%81%E9%83%A8%E5%88%86.html">
     3.1 思考：完成深度学习的必要部分
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.2%20%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE.html">
     3.2 基本配置
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.3%20%E6%95%B0%E6%8D%AE%E8%AF%BB%E5%85%A5.html">
     3.3 数据读入
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.4%20%E6%A8%A1%E5%9E%8B%E6%9E%84%E5%BB%BA.html">
     3.4 模型构建
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.5%20%E6%A8%A1%E5%9E%8B%E5%88%9D%E5%A7%8B%E5%8C%96.html">
     3.5 模型初始化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.6%20%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     3.6 损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.7%20%E8%AE%AD%E7%BB%83%E4%B8%8E%E8%AF%84%E4%BC%B0.html">
     3.7 训练和评估
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.8%20%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     3.8 可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B8%89%E7%AB%A0/3.9%20%E4%BC%98%E5%8C%96%E5%99%A8.html">
     3.9 PyTorch优化器
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/index.html">
   第四章：PyTorch基础实战
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-5" name="toctree-checkbox-5" type="checkbox"/>
  <label for="toctree-checkbox-5">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.1%20ResNet.html">
     4.1 ResNet
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%9B%9B%E7%AB%A0/4.4%20FashionMNIST%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     基础实战——FashionMNIST时装分类
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/index.html">
   第五章：PyTorch模型定义
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-6" name="toctree-checkbox-6" type="checkbox"/>
  <label for="toctree-checkbox-6">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.1%20PyTorch%E6%A8%A1%E5%9E%8B%E5%AE%9A%E4%B9%89%E7%9A%84%E6%96%B9%E5%BC%8F.html">
     5.1 PyTorch模型定义的方式
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.2%20%E5%88%A9%E7%94%A8%E6%A8%A1%E5%9E%8B%E5%9D%97%E5%BF%AB%E9%80%9F%E6%90%AD%E5%BB%BA%E5%A4%8D%E6%9D%82%E7%BD%91%E7%BB%9C.html">
     5.2 利用模型块快速搭建复杂网络
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.3%20PyTorch%E4%BF%AE%E6%94%B9%E6%A8%A1%E5%9E%8B.html">
     5.3 PyTorch修改模型
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%BA%94%E7%AB%A0/5.4%20PyTorh%E6%A8%A1%E5%9E%8B%E4%BF%9D%E5%AD%98%E4%B8%8E%E8%AF%BB%E5%8F%96.html">
     5.4 PyTorch模型保存与读取
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/index.html">
   第六章：PyTorch进阶训练技巧
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-7" name="toctree-checkbox-7" type="checkbox"/>
  <label for="toctree-checkbox-7">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.1%20%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8D%9F%E5%A4%B1%E5%87%BD%E6%95%B0.html">
     6.1 自定义损失函数
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.2%20%E5%8A%A8%E6%80%81%E8%B0%83%E6%95%B4%E5%AD%A6%E4%B9%A0%E7%8E%87.html">
     6.2 动态调整学习率
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-torchvision.html">
     6.3 模型微调-torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.3%20%E6%A8%A1%E5%9E%8B%E5%BE%AE%E8%B0%83-timm.html">
     6.3 模型微调 - timm
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.4%20%E5%8D%8A%E7%B2%BE%E5%BA%A6%E8%AE%AD%E7%BB%83.html">
     6.4 半精度训练
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.5%20%E6%95%B0%E6%8D%AE%E5%A2%9E%E5%BC%BA-imgaug.html">
     6.5 数据增强-imgaug
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AD%E7%AB%A0/6.6%20%E4%BD%BF%E7%94%A8argparse%E8%BF%9B%E8%A1%8C%E8%B0%83%E5%8F%82.html">
     6.6 使用argparse进行调参
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 current active has-children">
  <a class="reference internal" href="index.html">
   第七章：PyTorch可视化
  </a>
  <input checked="" class="toctree-checkbox" id="toctree-checkbox-8" name="toctree-checkbox-8" type="checkbox"/>
  <label for="toctree-checkbox-8">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul class="current">
   <li class="toctree-l2 current active">
    <a class="current reference internal" href="#">
     7.1 可视化网络结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html">
     7.2 CNN可视化
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="7.3%20%E4%BD%BF%E7%94%A8TensorBoard%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.3 使用TensorBoard可视化训练过程
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="7.4%20%E4%BD%BF%E7%94%A8wandb%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%AD%E7%BB%83%E8%BF%87%E7%A8%8B.html">
     7.4 使用wandb可视化训练过程
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/index.html">
   第八章：PyTorch生态简介
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-9" name="toctree-checkbox-9" type="checkbox"/>
  <label for="toctree-checkbox-9">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.1%20%E6%9C%AC%E7%AB%A0%E7%AE%80%E4%BB%8B.html">
     8.1 本章简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.2%20%E5%9B%BE%E5%83%8F%20-%20torchvision.html">
     8.2 torchvision
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.3%20%E8%A7%86%E9%A2%91%20-%20PyTorchVideo.html">
     8.3 PyTorchVideo简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.4%20%E6%96%87%E6%9C%AC%20-%20torchtext.html">
     8.4 torchtext简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%85%AB%E7%AB%A0/8.5%20%E9%9F%B3%E9%A2%91%20-%20torchaudio.html">
     8.5 torchaudio简介
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/index.html">
   第九章：PyTorch的模型部署
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-10" name="toctree-checkbox-10" type="checkbox"/>
  <label for="toctree-checkbox-10">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E4%B9%9D%E7%AB%A0/9.1%20%E4%BD%BF%E7%94%A8ONNX%E8%BF%9B%E8%A1%8C%E9%83%A8%E7%BD%B2%E5%B9%B6%E6%8E%A8%E7%90%86.html">
     9.1 使用ONNX进行部署并推理
    </a>
   </li>
  </ul>
 </li>
 <li class="toctree-l1 has-children">
  <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/index.html">
   第十章：常见代码解读
  </a>
  <input class="toctree-checkbox" id="toctree-checkbox-11" name="toctree-checkbox-11" type="checkbox"/>
  <label for="toctree-checkbox-11">
   <i class="fas fa-chevron-down">
   </i>
  </label>
  <ul>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.1%20%E5%9B%BE%E5%83%8F%E5%88%86%E7%B1%BB.html">
     10.1 图像分类简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.2%20%E7%9B%AE%E6%A0%87%E6%A3%80%E6%B5%8B.html">
     目标检测简介
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/10.3%20%E5%9B%BE%E5%83%8F%E5%88%86%E5%89%B2.html">
     10.3 图像分割简介（补充中）
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ResNet%E6%BA%90%E7%A0%81%E8%A7%A3%E8%AF%BB.html">
     ResNet源码解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/RNN%E8%AF%A6%E8%A7%A3%E5%8F%8A%E5%85%B6%E5%AE%9E%E7%8E%B0.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/LSTM%E8%A7%A3%E8%AF%BB%E5%8F%8A%E5%AE%9E%E6%88%98.html">
     文章结构
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Transformer%20%E8%A7%A3%E8%AF%BB.html">
     Transformer 解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/ViT%E8%A7%A3%E8%AF%BB.html">
     ViT解读
    </a>
   </li>
   <li class="toctree-l2">
    <a class="reference internal" href="../%E7%AC%AC%E5%8D%81%E7%AB%A0/Swin-Transformer%E8%A7%A3%E8%AF%BB.html">
     Swin Transformer解读
    </a>
   </li>
  </ul>
 </li>
</ul>

    </div>
</nav></div>
        <div class="bd-sidebar__bottom">
             <!-- To handle the deprecated key -->
            
            <div class="navbar_extra_footer">
            Theme by the <a href="https://ebp.jupyterbook.org">Executable Book Project</a>
            </div>
            
        </div>
    </div>
    <div id="rtd-footer-container"></div>
</div>


          


          
<!-- A tiny helper pixel to detect if we've scrolled -->
<div class="sbt-scroll-pixel-helper"></div>
<!-- Main content -->
<div class="col py-0 content-container">
    
    <div class="header-article row sticky-top noprint">
        



<div class="col py-1 d-flex header-article-main">
    <div class="header-article__left">
        
        <label for="__navigation"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="right"
title="Toggle navigation"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-bars"></i>
  </span>

</label>

        
    </div>
    <div class="header-article__right">
<button onclick="toggleFullScreen()"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="bottom"
title="Fullscreen mode"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-expand"></i>
  </span>

</button>

<div class="menu-dropdown menu-dropdown-repository-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Source repositories">
      <i class="fab fa-github"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Source repository"
>
  

<span class="headerbtn__icon-container">
  <i class="fab fa-github"></i>
  </span>
<span class="headerbtn__text-container">repository</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/issues/new?title=Issue%20on%20page%20%2F第七章/7.1 可视化网络结构.html&body=Your%20issue%20content%20here."
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Open an issue"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-lightbulb"></i>
  </span>
<span class="headerbtn__text-container">open issue</span>
</a>

      </li>
      
      <li>
        <a href="https://github.com/datawhalechina/thorough-pytorch/edit/master/第七章/7.1 可视化网络结构.md"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Edit this page"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-pencil-alt"></i>
  </span>
<span class="headerbtn__text-container">suggest edit</span>
</a>

      </li>
      
    </ul>
  </div>
</div>

<div class="menu-dropdown menu-dropdown-download-buttons">
  <button class="headerbtn menu-dropdown__trigger"
      aria-label="Download this page">
      <i class="fas fa-download"></i>
  </button>
  <div class="menu-dropdown__content">
    <ul>
      <li>
        <a href="../_sources/第七章/7.1 可视化网络结构.md.txt"
   class="headerbtn"
   data-toggle="tooltip"
data-placement="left"
title="Download source file"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file"></i>
  </span>
<span class="headerbtn__text-container">.md</span>
</a>

      </li>
      
      <li>
        
<button onclick="printPdf(this)"
  class="headerbtn"
  data-toggle="tooltip"
data-placement="left"
title="Print to PDF"
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-file-pdf"></i>
  </span>
<span class="headerbtn__text-container">.pdf</span>
</button>

      </li>
      
    </ul>
  </div>
</div>
<label for="__page-toc"
  class="headerbtn headerbtn-page-toc"
  
>
  

<span class="headerbtn__icon-container">
  <i class="fas fa-list"></i>
  </span>

</label>

    </div>
</div>

<!-- Table of contents -->
<div class="col-md-3 bd-toc show noprint">
    <div class="tocsection onthispage pt-5 pb-3">
        <i class="fas fa-list"></i> Contents
    </div>
    <nav id="bd-toc-nav" aria-label="Page">
        <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#print">
   7.1.1 使用print函数打印模型基础信息
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#torchinfo">
   7.1.2 使用torchinfo可视化网络结构
  </a>
 </li>
</ul>

    </nav>
</div>
    </div>
    <div class="article row">
        <div class="col pl-md-3 pl-lg-5 content-container">
            <!-- Table of contents that is only displayed when printing the page -->
            <div id="jb-print-docs-body" class="onlyprint">
                <h1>7.1 可视化网络结构</h1>
                <!-- Table of contents -->
                <div id="print-main-content">
                    <div id="jb-print-toc">
                        
                        <div>
                            <h2> Contents </h2>
                        </div>
                        <nav aria-label="Page">
                            <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#print">
   7.1.1 使用print函数打印模型基础信息
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#torchinfo">
   7.1.2 使用torchinfo可视化网络结构
  </a>
 </li>
</ul>

                        </nav>
                    </div>
                </div>
            </div>
            <main id="main-content" role="main">
                
              <div>
                
  <section class="tex2jax_ignore mathjax_ignore" id="id1">
<h1>7.1 可视化网络结构<a class="headerlink" href="#id1" title="永久链接至标题">#</a></h1>
<p>随着深度神经网络做的的发展，网络的结构越来越复杂，我们也很难确定每一层的输入结构，输出结构以及参数等信息，这样导致我们很难在短时间内完成debug。因此掌握一个可以用来可视化网络结构的工具是十分有必要的。类似的功能在另一个深度学习库Keras中可以调用一个叫做<code class="docutils literal notranslate"><span class="pre">model.summary()</span></code>的API来很方便地实现，调用后就会显示我们的模型参数，输入大小，输出大小，模型的整体参数等，但是在PyTorch中没有这样一种便利的工具帮助我们可视化我们的模型结构。</p>
<p>为了解决这个问题，人们开发了torchinfo工具包 ( torchinfo是由torchsummary和torchsummaryX重构出的库) 。本节我们将介绍如何使用torchinfo来可视化网络结构。</p>
<p>经过本节的学习，你将收获：</p>
<ul class="simple">
<li><p>可视化网络结构的方法</p></li>
</ul>
<section id="print">
<h2>7.1.1 使用print函数打印模型基础信息<a class="headerlink" href="#print" title="永久链接至标题">#</a></h2>
<p>在本节中，我们将使用ResNet18的结构进行展示。</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torchvision.models</span> <span class="k">as</span> <span class="nn">models</span>
<span class="n">model</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">resnet18</span><span class="p">()</span>
</pre></div>
</div>
<p>通过上面的两步，我们就得到resnet18的模型结构。在学习torchinfo之前，让我们先看下直接print(model)的结果。</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">ResNet</span><span class="p">(</span>
  <span class="p">(</span><span class="n">conv1</span><span class="p">):</span> <span class="n">Conv2d</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">kernel_size</span><span class="o">=</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">7</span><span class="p">),</span> <span class="n">stride</span><span class="o">=</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">2</span><span class="p">),</span> <span class="n">padding</span><span class="o">=</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span> <span class="n">bias</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
  <span class="p">(</span><span class="n">bn1</span><span class="p">):</span> <span class="n">BatchNorm2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="n">eps</span><span class="o">=</span><span class="mf">1e-05</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span> <span class="n">affine</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">track_running_stats</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
  <span class="p">(</span><span class="n">relu</span><span class="p">):</span> <span class="n">ReLU</span><span class="p">(</span><span class="n">inplace</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
  <span class="p">(</span><span class="n">maxpool</span><span class="p">):</span> <span class="n">MaxPool2d</span><span class="p">(</span><span class="n">kernel_size</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">stride</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">padding</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">dilation</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">ceil_mode</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
  <span class="p">(</span><span class="n">layer1</span><span class="p">):</span> <span class="n">Sequential</span><span class="p">(</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">):</span> <span class="n">Bottleneck</span><span class="p">(</span>
      <span class="p">(</span><span class="n">conv1</span><span class="p">):</span> <span class="n">Conv2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">kernel_size</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">stride</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">bias</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
      <span class="p">(</span><span class="n">bn1</span><span class="p">):</span> <span class="n">BatchNorm2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="n">eps</span><span class="o">=</span><span class="mf">1e-05</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span> <span class="n">affine</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">track_running_stats</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
      <span class="p">(</span><span class="n">conv2</span><span class="p">):</span> <span class="n">Conv2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">kernel_size</span><span class="o">=</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span> <span class="n">stride</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">padding</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">bias</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
      <span class="p">(</span><span class="n">bn2</span><span class="p">):</span> <span class="n">BatchNorm2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="n">eps</span><span class="o">=</span><span class="mf">1e-05</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span> <span class="n">affine</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">track_running_stats</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
      <span class="p">(</span><span class="n">conv3</span><span class="p">):</span> <span class="n">Conv2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="mi">256</span><span class="p">,</span> <span class="n">kernel_size</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">stride</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">bias</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
      <span class="p">(</span><span class="n">bn3</span><span class="p">):</span> <span class="n">BatchNorm2d</span><span class="p">(</span><span class="mi">256</span><span class="p">,</span> <span class="n">eps</span><span class="o">=</span><span class="mf">1e-05</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span> <span class="n">affine</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">track_running_stats</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
      <span class="p">(</span><span class="n">relu</span><span class="p">):</span> <span class="n">ReLU</span><span class="p">(</span><span class="n">inplace</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
      <span class="p">(</span><span class="n">downsample</span><span class="p">):</span> <span class="n">Sequential</span><span class="p">(</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">):</span> <span class="n">Conv2d</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="mi">256</span><span class="p">,</span> <span class="n">kernel_size</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">stride</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span> <span class="n">bias</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
        <span class="p">(</span><span class="mi">1</span><span class="p">):</span> <span class="n">BatchNorm2d</span><span class="p">(</span><span class="mi">256</span><span class="p">,</span> <span class="n">eps</span><span class="o">=</span><span class="mf">1e-05</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span> <span class="n">affine</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">track_running_stats</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
      <span class="p">)</span>
    <span class="p">)</span>
   <span class="o">...</span> <span class="o">...</span>
  <span class="p">)</span>
  <span class="p">(</span><span class="n">avgpool</span><span class="p">):</span> <span class="n">AdaptiveAvgPool2d</span><span class="p">(</span><span class="n">output_size</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">))</span>
  <span class="p">(</span><span class="n">fc</span><span class="p">):</span> <span class="n">Linear</span><span class="p">(</span><span class="n">in_features</span><span class="o">=</span><span class="mi">2048</span><span class="p">,</span> <span class="n">out_features</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">)</span>
</pre></div>
</div>
<p>我们可以发现单纯的print(model)，只能得出基础构件的信息，既不能显示出每一层的shape，也不能显示对应参数量的大小，为了解决这些问题，我们就需要介绍出我们今天的主人公<code class="docutils literal notranslate"><span class="pre">torchinfo</span></code>。</p>
</section>
<section id="torchinfo">
<h2>7.1.2 使用torchinfo可视化网络结构<a class="headerlink" href="#torchinfo" title="永久链接至标题">#</a></h2>
<ul class="simple">
<li><p><strong>torchinfo的安装</strong></p></li>
</ul>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 安装方法一</span>
pip install torchinfo 
<span class="c1"># 安装方法二</span>
conda install -c conda-forge torchinfo
</pre></div>
</div>
<ul class="simple">
<li><p><strong>torchinfo的使用</strong></p></li>
</ul>
<p>trochinfo的使用也是十分简单，我们只需要使用<code class="docutils literal notranslate"><span class="pre">torchinfo.summary()</span></code>就行了，必需的参数分别是model，input_size[batch_size,channel,h,w]，更多参数可以参考<a class="reference external" href="https://github.com/TylerYep/torchinfo#documentation">documentation</a>，下面让我们一起通过一个实例进行学习。</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torchvision.models</span> <span class="k">as</span> <span class="nn">models</span>
<span class="kn">from</span> <span class="nn">torchinfo</span> <span class="kn">import</span> <span class="n">summary</span>
<span class="n">resnet18</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">resnet18</span><span class="p">()</span> <span class="c1"># 实例化模型</span>
<span class="n">summary</span><span class="p">(</span><span class="n">resnet18</span><span class="p">,</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">224</span><span class="p">,</span> <span class="mi">224</span><span class="p">))</span> <span class="c1"># 1：batch_size 3:图片的通道数 224: 图片的高宽</span>
</pre></div>
</div>
<ul class="simple">
<li><p><strong>torchinfo的结构化输出</strong></p></li>
</ul>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="o">=========================================================================================</span>
Layer <span class="o">(</span>type:depth-idx<span class="o">)</span>                   Output Shape              Param <span class="c1">#</span>
<span class="o">=========================================================================================</span>
ResNet                                   --                        --
├─Conv2d: <span class="m">1</span>-1                            <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">112</span>, <span class="m">112</span><span class="o">]</span>         <span class="m">9</span>,408
├─BatchNorm2d: <span class="m">1</span>-2                       <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">112</span>, <span class="m">112</span><span class="o">]</span>         <span class="m">128</span>
├─ReLU: <span class="m">1</span>-3                              <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">112</span>, <span class="m">112</span><span class="o">]</span>         --
├─MaxPool2d: <span class="m">1</span>-4                         <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
├─Sequential: <span class="m">1</span>-5                        <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    └─BasicBlock: <span class="m">2</span>-1                   <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    │    └─Conv2d: <span class="m">3</span>-1                  <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">36</span>,864
│    │    └─BatchNorm2d: <span class="m">3</span>-2             <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">128</span>
│    │    └─ReLU: <span class="m">3</span>-3                    <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    │    └─Conv2d: <span class="m">3</span>-4                  <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">36</span>,864
│    │    └─BatchNorm2d: <span class="m">3</span>-5             <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">128</span>
│    │    └─ReLU: <span class="m">3</span>-6                    <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    └─BasicBlock: <span class="m">2</span>-2                   <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    │    └─Conv2d: <span class="m">3</span>-7                  <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">36</span>,864
│    │    └─BatchNorm2d: <span class="m">3</span>-8             <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">128</span>
│    │    └─ReLU: <span class="m">3</span>-9                    <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
│    │    └─Conv2d: <span class="m">3</span>-10                 <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">36</span>,864
│    │    └─BatchNorm2d: <span class="m">3</span>-11            <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           <span class="m">128</span>
│    │    └─ReLU: <span class="m">3</span>-12                   <span class="o">[</span><span class="m">1</span>, <span class="m">64</span>, <span class="m">56</span>, <span class="m">56</span><span class="o">]</span>           --
├─Sequential: <span class="m">1</span>-6                        <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    └─BasicBlock: <span class="m">2</span>-3                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-13                 <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">73</span>,728
│    │    └─BatchNorm2d: <span class="m">3</span>-14            <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">256</span>
│    │    └─ReLU: <span class="m">3</span>-15                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-16                 <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">147</span>,456
│    │    └─BatchNorm2d: <span class="m">3</span>-17            <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">256</span>
│    │    └─Sequential: <span class="m">3</span>-18             <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">8</span>,448
│    │    └─ReLU: <span class="m">3</span>-19                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    └─BasicBlock: <span class="m">2</span>-4                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-20                 <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">147</span>,456
│    │    └─BatchNorm2d: <span class="m">3</span>-21            <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">256</span>
│    │    └─ReLU: <span class="m">3</span>-22                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-23                 <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">147</span>,456
│    │    └─BatchNorm2d: <span class="m">3</span>-24            <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          <span class="m">256</span>
│    │    └─ReLU: <span class="m">3</span>-25                   <span class="o">[</span><span class="m">1</span>, <span class="m">128</span>, <span class="m">28</span>, <span class="m">28</span><span class="o">]</span>          --
├─Sequential: <span class="m">1</span>-7                        <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    └─BasicBlock: <span class="m">2</span>-5                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-26                 <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">294</span>,912
│    │    └─BatchNorm2d: <span class="m">3</span>-27            <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">512</span>
│    │    └─ReLU: <span class="m">3</span>-28                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-29                 <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">589</span>,824
│    │    └─BatchNorm2d: <span class="m">3</span>-30            <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">512</span>
│    │    └─Sequential: <span class="m">3</span>-31             <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">33</span>,280
│    │    └─ReLU: <span class="m">3</span>-32                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    └─BasicBlock: <span class="m">2</span>-6                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-33                 <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">589</span>,824
│    │    └─BatchNorm2d: <span class="m">3</span>-34            <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">512</span>
│    │    └─ReLU: <span class="m">3</span>-35                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
│    │    └─Conv2d: <span class="m">3</span>-36                 <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">589</span>,824
│    │    └─BatchNorm2d: <span class="m">3</span>-37            <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          <span class="m">512</span>
│    │    └─ReLU: <span class="m">3</span>-38                   <span class="o">[</span><span class="m">1</span>, <span class="m">256</span>, <span class="m">14</span>, <span class="m">14</span><span class="o">]</span>          --
├─Sequential: <span class="m">1</span>-8                        <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    └─BasicBlock: <span class="m">2</span>-7                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    │    └─Conv2d: <span class="m">3</span>-39                 <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">1</span>,179,648
│    │    └─BatchNorm2d: <span class="m">3</span>-40            <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">1</span>,024
│    │    └─ReLU: <span class="m">3</span>-41                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    │    └─Conv2d: <span class="m">3</span>-42                 <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">2</span>,359,296
│    │    └─BatchNorm2d: <span class="m">3</span>-43            <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">1</span>,024
│    │    └─Sequential: <span class="m">3</span>-44             <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">132</span>,096
│    │    └─ReLU: <span class="m">3</span>-45                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    └─BasicBlock: <span class="m">2</span>-8                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    │    └─Conv2d: <span class="m">3</span>-46                 <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">2</span>,359,296
│    │    └─BatchNorm2d: <span class="m">3</span>-47            <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">1</span>,024
│    │    └─ReLU: <span class="m">3</span>-48                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
│    │    └─Conv2d: <span class="m">3</span>-49                 <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">2</span>,359,296
│    │    └─BatchNorm2d: <span class="m">3</span>-50            <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            <span class="m">1</span>,024
│    │    └─ReLU: <span class="m">3</span>-51                   <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">7</span>, <span class="m">7</span><span class="o">]</span>            --
├─AdaptiveAvgPool2d: <span class="m">1</span>-9                 <span class="o">[</span><span class="m">1</span>, <span class="m">512</span>, <span class="m">1</span>, <span class="m">1</span><span class="o">]</span>            --
├─Linear: <span class="m">1</span>-10                           <span class="o">[</span><span class="m">1</span>, <span class="m">1000</span><span class="o">]</span>                 <span class="m">513</span>,000
<span class="o">=========================================================================================</span>
Total params: <span class="m">11</span>,689,512
Trainable params: <span class="m">11</span>,689,512
Non-trainable params: <span class="m">0</span>
Total mult-adds <span class="o">(</span>G<span class="o">)</span>: <span class="m">1</span>.81
<span class="o">=========================================================================================</span>
Input size <span class="o">(</span>MB<span class="o">)</span>: <span class="m">0</span>.60
Forward/backward pass size <span class="o">(</span>MB<span class="o">)</span>: <span class="m">39</span>.75
Params size <span class="o">(</span>MB<span class="o">)</span>: <span class="m">46</span>.76
Estimated Total Size <span class="o">(</span>MB<span class="o">)</span>: <span class="m">87</span>.11
<span class="o">=========================================================================================</span>
</pre></div>
</div>
<p>我们可以看到torchinfo提供了更加详细的信息，包括模块信息（每一层的类型、输出shape和参数量）、模型整体的参数量、模型大小、一次前向或者反向传播需要的内存大小等</p>
<p><strong>注意</strong>：</p>
<p>但你使用的是colab或者jupyter notebook时，想要实现该方法，<code class="docutils literal notranslate"><span class="pre">summary()</span></code>一定是该单元（即notebook中的cell）的返回值，否则我们就需要使用<code class="docutils literal notranslate"><span class="pre">print(summary(...))</span></code>来可视化。</p>
</section>
</section>


              </div>
              
            </main>
            <footer class="footer-article noprint">
                
    <!-- Previous / next buttons -->
<div class='prev-next-area'>
    <a class='left-prev' id="prev-link" href="index.html" title="上一页 页">
        <i class="fas fa-angle-left"></i>
        <div class="prev-next-info">
            <p class="prev-next-subtitle">上一页</p>
            <p class="prev-next-title">第七章：PyTorch可视化</p>
        </div>
    </a>
    <a class='right-next' id="next-link" href="7.2%20CNN%E5%8D%B7%E7%A7%AF%E5%B1%82%E5%8F%AF%E8%A7%86%E5%8C%96.html" title="下一页 页">
    <div class="prev-next-info">
        <p class="prev-next-subtitle">下一页</p>
        <p class="prev-next-title">7.2 CNN可视化</p>
    </div>
    <i class="fas fa-angle-right"></i>
    </a>
</div>
            </footer>
        </div>
    </div>
    <div class="footer-content row">
        <footer class="col footer"><p>
  
    By ZhikangNiu<br/>
  
      &copy; Copyright 2022, ZhikangNiu.<br/>
</p>
        </footer>
    </div>
    
</div>


      </div>
    </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="../_static/scripts/pydata-sphinx-theme.js?digest=1999514e3f237ded88cf"></script>


  </body>
</html>