Package: IAN
Type: Package
Title: IAN : Integrated Enrichment Analysis Using A Multi-Agent AI System
Version: 0.1.0
Author: <PERSON> [aut, cre]
Maintainer: <PERSON> <<EMAIL>>
Description: IAN is an R package for integrated enrichment analysis, leveraging a multi-agent AI system and Large Language Models. It summarizes and interprets enrichment results from various "Omics" data sources (Seurat, DESeq2, etc.) using pathway databases and protein-protein interaction data. IAN provides insightful interpretations and novel observations in an easy-to-navigate HTML report. It helps researchers explore and decipher complex biological systems by augmenting LLM summarization abilities.
License: file LICENSE
Encoding: UTF-8
LazyData: true
Imports:
    igraph,
    BiocManager,
    clusterProfiler,
    dplyr,
    plyr,
    tidyr,
    httr,
    visNetwork,
    ReactomePA,
    STRINGdb,
    enrichR,
    furrr,
    future,
    progressr,
    readr,
    stringr,
    methods,
	rmarkdown    
Suggests: 
    testthat (>= 3.0.0)
RoxygenNote: 7.3.2
