/* Whole cell */
div.container.cell {
  padding-left: 0;
  margin-bottom: 1em;
}

/* Removing all background formatting so we can control at the div level */
.cell_input div.highlight, .cell_output pre, .cell_input pre, .cell_output .output {
  border: none;
  box-shadow: none;
}

.cell_output .output pre, .cell_input pre {
  margin: 0px;
}

/* Input cells */
div.cell div.cell_input {
  padding-left: 0em;
  padding-right: 0em;
  border: 1px #ccc solid;
  background-color: #f7f7f7;
  border-left-color: green;
  border-left-width: medium;
}

div.cell_input > div, div.cell_output div.output > div.highlight {
  margin: 0em !important;
  border: none !important;
}

/* All cell outputs */
.cell_output {
  padding-left: 1em;
  padding-right: 0em;
  margin-top: 1em;
}

/* Outputs from jupyter_sphinx overrides to remove extra CSS */
div.section div.jupyter_container {
  padding: .4em;
  margin: 0 0 .4em 0;
  background-color: none;
  border: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/* Text outputs from cells */
.cell_output .output.text_plain,
.cell_output .output.traceback,
.cell_output .output.stream,
.cell_output .output.stderr
  {
  background: #fcfcfc;
  margin-top: 1em;
  margin-bottom: 0em;
  box-shadow: none;
}

.cell_output .output.text_plain,
.cell_output .output.stream,
.cell_output .output.stderr {
  border: 1px solid #f7f7f7;
}

.cell_output .output.stderr {
  background: #fdd;
}

.cell_output .output.traceback {
  border: 1px solid #ffd6d6;
}

/* Math align to the left */
.cell_output .MathJax_Display {
  text-align: left !important;
}

/* Pandas tables. Pulled from the Jupyter / nbsphinx CSS */
div.cell_output table {
    border: none;
    border-collapse: collapse;
    border-spacing: 0;
    color: black;
    font-size: 1em;
    table-layout: fixed;
  }
  div.cell_output thead {
    border-bottom: 1px solid black;
    vertical-align: bottom;
  }
  div.cell_output tr,
  div.cell_output th,
  div.cell_output td {
    text-align: right;
    vertical-align: middle;
    padding: 0.5em 0.5em;
    line-height: normal;
    white-space: normal;
    max-width: none;
    border: none;
  }
  div.cell_output th {
    font-weight: bold;
  }
  div.cell_output tbody tr:nth-child(odd) {
    background: #f5f5f5;
  }
  div.cell_output tbody tr:hover {
    background: rgba(66, 165, 245, 0.2);
  }


/* Inline text from `paste` operation */

span.pasted-text {
  font-weight: bold;
}

span.pasted-inline img {
  max-height: 2em;
}

tbody span.pasted-inline img {
  max-height: none;
}

/* Font colors for translated ANSI escape sequences
Color values are adapted from share/jupyter/nbconvert/templates/classic/static/style.css
*/
div.highlight .-Color-Bold {
  font-weight: bold;
}
div.highlight .-Color[class*=-Black] {
  color :#3E424D
}
div.highlight .-Color[class*=-Red] {
  color: #E75C58
}
div.highlight .-Color[class*=-Green] {
  color: #00A250
}
div.highlight .-Color[class*=-Yellow] {
  color: yellow
}
div.highlight .-Color[class*=-Blue] {
  color: #208FFB
}
div.highlight .-Color[class*=-Magenta] {
  color: #D160C4
}
div.highlight .-Color[class*=-Cyan] {
  color: #60C6C8
}
div.highlight .-Color[class*=-White] {
  color: #C5C1B4
}
div.highlight .-Color[class*=-BGBlack] {
  background-color: #3E424D
}
div.highlight .-Color[class*=-BGRed] {
  background-color: #E75C58
}
div.highlight .-Color[class*=-BGGreen] {
  background-color: #00A250
}
div.highlight .-Color[class*=-BGYellow] {
  background-color: yellow
}
div.highlight .-Color[class*=-BGBlue] {
  background-color: #208FFB
}
div.highlight .-Color[class*=-BGMagenta] {
  background-color: #D160C4
}
div.highlight .-Color[class*=-BGCyan] {
  background-color: #60C6C8
}
div.highlight .-Color[class*=-BGWhite] {
  background-color: #C5C1B4
}

/* Font colors for 8-bit ANSI */

div.highlight .-Color[class*=-C0] {
  color: #000000
}
div.highlight .-Color[class*=-BGC0] {
  background-color: #000000
}
div.highlight .-Color[class*=-C1] {
  color: #800000
}
div.highlight .-Color[class*=-BGC1] {
  background-color: #800000
}
div.highlight .-Color[class*=-C2] {
  color: #008000
}
div.highlight .-Color[class*=-BGC2] {
  background-color: #008000
}
div.highlight .-Color[class*=-C3] {
  color: #808000
}
div.highlight .-Color[class*=-BGC3] {
  background-color: #808000
}
div.highlight .-Color[class*=-C4] {
  color: #000080
}
div.highlight .-Color[class*=-BGC4] {
  background-color: #000080
}
div.highlight .-Color[class*=-C5] {
  color: #800080
}
div.highlight .-Color[class*=-BGC5] {
  background-color: #800080
}
div.highlight .-Color[class*=-C6] {
  color: #008080
}
div.highlight .-Color[class*=-BGC6] {
  background-color: #008080
}
div.highlight .-Color[class*=-C7] {
  color: #C0C0C0
}
div.highlight .-Color[class*=-BGC7] {
  background-color: #C0C0C0
}
div.highlight .-Color[class*=-C8] {
  color: #808080
}
div.highlight .-Color[class*=-BGC8] {
  background-color: #808080
}
div.highlight .-Color[class*=-C9] {
  color: #FF0000
}
div.highlight .-Color[class*=-BGC9] {
  background-color: #FF0000
}
div.highlight .-Color[class*=-C10] {
  color: #00FF00
}
div.highlight .-Color[class*=-BGC10] {
  background-color: #00FF00
}
div.highlight .-Color[class*=-C11] {
  color: #FFFF00
}
div.highlight .-Color[class*=-BGC11] {
  background-color: #FFFF00
}
div.highlight .-Color[class*=-C12] {
  color: #0000FF
}
div.highlight .-Color[class*=-BGC12] {
  background-color: #0000FF
}
div.highlight .-Color[class*=-C13] {
  color: #FF00FF
}
div.highlight .-Color[class*=-BGC13] {
  background-color: #FF00FF
}
div.highlight .-Color[class*=-C14] {
  color: #00FFFF
}
div.highlight .-Color[class*=-BGC14] {
  background-color: #00FFFF
}
div.highlight .-Color[class*=-C15] {
  color: #FFFFFF
}
div.highlight .-Color[class*=-BGC15] {
  background-color: #FFFFFF
}
div.highlight .-Color[class*=-C16] {
  color: #000000
}
div.highlight .-Color[class*=-BGC16] {
  background-color: #000000
}
div.highlight .-Color[class*=-C17] {
  color: #00005F
}
div.highlight .-Color[class*=-BGC17] {
  background-color: #00005F
}
div.highlight .-Color[class*=-C18] {
  color: #000087
}
div.highlight .-Color[class*=-BGC18] {
  background-color: #000087
}
div.highlight .-Color[class*=-C19] {
  color: #0000AF
}
div.highlight .-Color[class*=-BGC19] {
  background-color: #0000AF
}
div.highlight .-Color[class*=-C20] {
  color: #0000D7
}
div.highlight .-Color[class*=-BGC20] {
  background-color: #0000D7
}
div.highlight .-Color[class*=-C21] {
  color: #0000FF
}
div.highlight .-Color[class*=-BGC21] {
  background-color: #0000FF
}
div.highlight .-Color[class*=-C22] {
  color: #005F00
}
div.highlight .-Color[class*=-BGC22] {
  background-color: #005F00
}
div.highlight .-Color[class*=-C23] {
  color: #005F5F
}
div.highlight .-Color[class*=-BGC23] {
  background-color: #005F5F
}
div.highlight .-Color[class*=-C24] {
  color: #005F87
}
div.highlight .-Color[class*=-BGC24] {
  background-color: #005F87
}
div.highlight .-Color[class*=-C25] {
  color: #005FAF
}
div.highlight .-Color[class*=-BGC25] {
  background-color: #005FAF
}
div.highlight .-Color[class*=-C26] {
  color: #005FD7
}
div.highlight .-Color[class*=-BGC26] {
  background-color: #005FD7
}
div.highlight .-Color[class*=-C27] {
  color: #005FFF
}
div.highlight .-Color[class*=-BGC27] {
  background-color: #005FFF
}
div.highlight .-Color[class*=-C28] {
  color: #008700
}
div.highlight .-Color[class*=-BGC28] {
  background-color: #008700
}
div.highlight .-Color[class*=-C29] {
  color: #00875F
}
div.highlight .-Color[class*=-BGC29] {
  background-color: #00875F
}
div.highlight .-Color[class*=-C30] {
  color: #008787
}
div.highlight .-Color[class*=-BGC30] {
  background-color: #008787
}
div.highlight .-Color[class*=-C31] {
  color: #0087AF
}
div.highlight .-Color[class*=-BGC31] {
  background-color: #0087AF
}
div.highlight .-Color[class*=-C32] {
  color: #0087D7
}
div.highlight .-Color[class*=-BGC32] {
  background-color: #0087D7
}
div.highlight .-Color[class*=-C33] {
  color: #0087FF
}
div.highlight .-Color[class*=-BGC33] {
  background-color: #0087FF
}
div.highlight .-Color[class*=-C34] {
  color: #00AF00
}
div.highlight .-Color[class*=-BGC34] {
  background-color: #00AF00
}
div.highlight .-Color[class*=-C35] {
  color: #00AF5F
}
div.highlight .-Color[class*=-BGC35] {
  background-color: #00AF5F
}
div.highlight .-Color[class*=-C36] {
  color: #00AF87
}
div.highlight .-Color[class*=-BGC36] {
  background-color: #00AF87
}
div.highlight .-Color[class*=-C37] {
  color: #00AFAF
}
div.highlight .-Color[class*=-BGC37] {
  background-color: #00AFAF
}
div.highlight .-Color[class*=-C38] {
  color: #00AFD7
}
div.highlight .-Color[class*=-BGC38] {
  background-color: #00AFD7
}
div.highlight .-Color[class*=-C39] {
  color: #00AFFF
}
div.highlight .-Color[class*=-BGC39] {
  background-color: #00AFFF
}
div.highlight .-Color[class*=-C40] {
  color: #00D700
}
div.highlight .-Color[class*=-BGC40] {
  background-color: #00D700
}
div.highlight .-Color[class*=-C41] {
  color: #00D75F
}
div.highlight .-Color[class*=-BGC41] {
  background-color: #00D75F
}
div.highlight .-Color[class*=-C42] {
  color: #00D787
}
div.highlight .-Color[class*=-BGC42] {
  background-color: #00D787
}
div.highlight .-Color[class*=-C43] {
  color: #00D7AF
}
div.highlight .-Color[class*=-BGC43] {
  background-color: #00D7AF
}
div.highlight .-Color[class*=-C44] {
  color: #00D7D7
}
div.highlight .-Color[class*=-BGC44] {
  background-color: #00D7D7
}
div.highlight .-Color[class*=-C45] {
  color: #00D7FF
}
div.highlight .-Color[class*=-BGC45] {
  background-color: #00D7FF
}
div.highlight .-Color[class*=-C46] {
  color: #00FF00
}
div.highlight .-Color[class*=-BGC46] {
  background-color: #00FF00
}
div.highlight .-Color[class*=-C47] {
  color: #00FF5F
}
div.highlight .-Color[class*=-BGC47] {
  background-color: #00FF5F
}
div.highlight .-Color[class*=-C48] {
  color: #00FF87
}
div.highlight .-Color[class*=-BGC48] {
  background-color: #00FF87
}
div.highlight .-Color[class*=-C49] {
  color: #00FFAF
}
div.highlight .-Color[class*=-BGC49] {
  background-color: #00FFAF
}
div.highlight .-Color[class*=-C50] {
  color: #00FFD7
}
div.highlight .-Color[class*=-BGC50] {
  background-color: #00FFD7
}
div.highlight .-Color[class*=-C51] {
  color: #00FFFF
}
div.highlight .-Color[class*=-BGC51] {
  background-color: #00FFFF
}
div.highlight .-Color[class*=-C52] {
  color: #5F0000
}
div.highlight .-Color[class*=-BGC52] {
  background-color: #5F0000
}
div.highlight .-Color[class*=-C53] {
  color: #5F005F
}
div.highlight .-Color[class*=-BGC53] {
  background-color: #5F005F
}
div.highlight .-Color[class*=-C54] {
  color: #5F0087
}
div.highlight .-Color[class*=-BGC54] {
  background-color: #5F0087
}
div.highlight .-Color[class*=-C55] {
  color: #5F00AF
}
div.highlight .-Color[class*=-BGC55] {
  background-color: #5F00AF
}
div.highlight .-Color[class*=-C56] {
  color: #5F00D7
}
div.highlight .-Color[class*=-BGC56] {
  background-color: #5F00D7
}
div.highlight .-Color[class*=-C57] {
  color: #5F00FF
}
div.highlight .-Color[class*=-BGC57] {
  background-color: #5F00FF
}
div.highlight .-Color[class*=-C58] {
  color: #5F5F00
}
div.highlight .-Color[class*=-BGC58] {
  background-color: #5F5F00
}
div.highlight .-Color[class*=-C59] {
  color: #5F5F5F
}
div.highlight .-Color[class*=-BGC59] {
  background-color: #5F5F5F
}
div.highlight .-Color[class*=-C60] {
  color: #5F5F87
}
div.highlight .-Color[class*=-BGC60] {
  background-color: #5F5F87
}
div.highlight .-Color[class*=-C61] {
  color: #5F5FAF
}
div.highlight .-Color[class*=-BGC61] {
  background-color: #5F5FAF
}
div.highlight .-Color[class*=-C62] {
  color: #5F5FD7
}
div.highlight .-Color[class*=-BGC62] {
  background-color: #5F5FD7
}
div.highlight .-Color[class*=-C63] {
  color: #5F5FFF
}
div.highlight .-Color[class*=-BGC63] {
  background-color: #5F5FFF
}
div.highlight .-Color[class*=-C64] {
  color: #5F8700
}
div.highlight .-Color[class*=-BGC64] {
  background-color: #5F8700
}
div.highlight .-Color[class*=-C65] {
  color: #5F875F
}
div.highlight .-Color[class*=-BGC65] {
  background-color: #5F875F
}
div.highlight .-Color[class*=-C66] {
  color: #5F8787
}
div.highlight .-Color[class*=-BGC66] {
  background-color: #5F8787
}
div.highlight .-Color[class*=-C67] {
  color: #5F87AF
}
div.highlight .-Color[class*=-BGC67] {
  background-color: #5F87AF
}
div.highlight .-Color[class*=-C68] {
  color: #5F87D7
}
div.highlight .-Color[class*=-BGC68] {
  background-color: #5F87D7
}
div.highlight .-Color[class*=-C69] {
  color: #5F87FF
}
div.highlight .-Color[class*=-BGC69] {
  background-color: #5F87FF
}
div.highlight .-Color[class*=-C70] {
  color: #5FAF00
}
div.highlight .-Color[class*=-BGC70] {
  background-color: #5FAF00
}
div.highlight .-Color[class*=-C71] {
  color: #5FAF5F
}
div.highlight .-Color[class*=-BGC71] {
  background-color: #5FAF5F
}
div.highlight .-Color[class*=-C72] {
  color: #5FAF87
}
div.highlight .-Color[class*=-BGC72] {
  background-color: #5FAF87
}
div.highlight .-Color[class*=-C73] {
  color: #5FAFAF
}
div.highlight .-Color[class*=-BGC73] {
  background-color: #5FAFAF
}
div.highlight .-Color[class*=-C74] {
  color: #5FAFD7
}
div.highlight .-Color[class*=-BGC74] {
  background-color: #5FAFD7
}
div.highlight .-Color[class*=-C75] {
  color: #5FAFFF
}
div.highlight .-Color[class*=-BGC75] {
  background-color: #5FAFFF
}
div.highlight .-Color[class*=-C76] {
  color: #5FD700
}
div.highlight .-Color[class*=-BGC76] {
  background-color: #5FD700
}
div.highlight .-Color[class*=-C77] {
  color: #5FD75F
}
div.highlight .-Color[class*=-BGC77] {
  background-color: #5FD75F
}
div.highlight .-Color[class*=-C78] {
  color: #5FD787
}
div.highlight .-Color[class*=-BGC78] {
  background-color: #5FD787
}
div.highlight .-Color[class*=-C79] {
  color: #5FD7AF
}
div.highlight .-Color[class*=-BGC79] {
  background-color: #5FD7AF
}
div.highlight .-Color[class*=-C80] {
  color: #5FD7D7
}
div.highlight .-Color[class*=-BGC80] {
  background-color: #5FD7D7
}
div.highlight .-Color[class*=-C81] {
  color: #5FD7FF
}
div.highlight .-Color[class*=-BGC81] {
  background-color: #5FD7FF
}
div.highlight .-Color[class*=-C82] {
  color: #5FFF00
}
div.highlight .-Color[class*=-BGC82] {
  background-color: #5FFF00
}
div.highlight .-Color[class*=-C83] {
  color: #5FFF5F
}
div.highlight .-Color[class*=-BGC83] {
  background-color: #5FFF5F
}
div.highlight .-Color[class*=-C84] {
  color: #5FFF87
}
div.highlight .-Color[class*=-BGC84] {
  background-color: #5FFF87
}
div.highlight .-Color[class*=-C85] {
  color: #5FFFAF
}
div.highlight .-Color[class*=-BGC85] {
  background-color: #5FFFAF
}
div.highlight .-Color[class*=-C86] {
  color: #5FFFD7
}
div.highlight .-Color[class*=-BGC86] {
  background-color: #5FFFD7
}
div.highlight .-Color[class*=-C87] {
  color: #5FFFFF
}
div.highlight .-Color[class*=-BGC87] {
  background-color: #5FFFFF
}
div.highlight .-Color[class*=-C88] {
  color: #870000
}
div.highlight .-Color[class*=-BGC88] {
  background-color: #870000
}
div.highlight .-Color[class*=-C89] {
  color: #87005F
}
div.highlight .-Color[class*=-BGC89] {
  background-color: #87005F
}
div.highlight .-Color[class*=-C90] {
  color: #870087
}
div.highlight .-Color[class*=-BGC90] {
  background-color: #870087
}
div.highlight .-Color[class*=-C91] {
  color: #8700AF
}
div.highlight .-Color[class*=-BGC91] {
  background-color: #8700AF
}
div.highlight .-Color[class*=-C92] {
  color: #8700D7
}
div.highlight .-Color[class*=-BGC92] {
  background-color: #8700D7
}
div.highlight .-Color[class*=-C93] {
  color: #8700FF
}
div.highlight .-Color[class*=-BGC93] {
  background-color: #8700FF
}
div.highlight .-Color[class*=-C94] {
  color: #875F00
}
div.highlight .-Color[class*=-BGC94] {
  background-color: #875F00
}
div.highlight .-Color[class*=-C95] {
  color: #875F5F
}
div.highlight .-Color[class*=-BGC95] {
  background-color: #875F5F
}
div.highlight .-Color[class*=-C96] {
  color: #875F87
}
div.highlight .-Color[class*=-BGC96] {
  background-color: #875F87
}
div.highlight .-Color[class*=-C97] {
  color: #875FAF
}
div.highlight .-Color[class*=-BGC97] {
  background-color: #875FAF
}
div.highlight .-Color[class*=-C98] {
  color: #875FD7
}
div.highlight .-Color[class*=-BGC98] {
  background-color: #875FD7
}
div.highlight .-Color[class*=-C99] {
  color: #875FFF
}
div.highlight .-Color[class*=-BGC99] {
  background-color: #875FFF
}
div.highlight .-Color[class*=-C100] {
  color: #878700
}
div.highlight .-Color[class*=-BGC100] {
  background-color: #878700
}
div.highlight .-Color[class*=-C101] {
  color: #87875F
}
div.highlight .-Color[class*=-BGC101] {
  background-color: #87875F
}
div.highlight .-Color[class*=-C102] {
  color: #878787
}
div.highlight .-Color[class*=-BGC102] {
  background-color: #878787
}
div.highlight .-Color[class*=-C103] {
  color: #8787AF
}
div.highlight .-Color[class*=-BGC103] {
  background-color: #8787AF
}
div.highlight .-Color[class*=-C104] {
  color: #8787D7
}
div.highlight .-Color[class*=-BGC104] {
  background-color: #8787D7
}
div.highlight .-Color[class*=-C105] {
  color: #8787FF
}
div.highlight .-Color[class*=-BGC105] {
  background-color: #8787FF
}
div.highlight .-Color[class*=-C106] {
  color: #87AF00
}
div.highlight .-Color[class*=-BGC106] {
  background-color: #87AF00
}
div.highlight .-Color[class*=-C107] {
  color: #87AF5F
}
div.highlight .-Color[class*=-BGC107] {
  background-color: #87AF5F
}
div.highlight .-Color[class*=-C108] {
  color: #87AF87
}
div.highlight .-Color[class*=-BGC108] {
  background-color: #87AF87
}
div.highlight .-Color[class*=-C109] {
  color: #87AFAF
}
div.highlight .-Color[class*=-BGC109] {
  background-color: #87AFAF
}
div.highlight .-Color[class*=-C110] {
  color: #87AFD7
}
div.highlight .-Color[class*=-BGC110] {
  background-color: #87AFD7
}
div.highlight .-Color[class*=-C111] {
  color: #87AFFF
}
div.highlight .-Color[class*=-BGC111] {
  background-color: #87AFFF
}
div.highlight .-Color[class*=-C112] {
  color: #87D700
}
div.highlight .-Color[class*=-BGC112] {
  background-color: #87D700
}
div.highlight .-Color[class*=-C113] {
  color: #87D75F
}
div.highlight .-Color[class*=-BGC113] {
  background-color: #87D75F
}
div.highlight .-Color[class*=-C114] {
  color: #87D787
}
div.highlight .-Color[class*=-BGC114] {
  background-color: #87D787
}
div.highlight .-Color[class*=-C115] {
  color: #87D7AF
}
div.highlight .-Color[class*=-BGC115] {
  background-color: #87D7AF
}
div.highlight .-Color[class*=-C116] {
  color: #87D7D7
}
div.highlight .-Color[class*=-BGC116] {
  background-color: #87D7D7
}
div.highlight .-Color[class*=-C117] {
  color: #87D7FF
}
div.highlight .-Color[class*=-BGC117] {
  background-color: #87D7FF
}
div.highlight .-Color[class*=-C118] {
  color: #87FF00
}
div.highlight .-Color[class*=-BGC118] {
  background-color: #87FF00
}
div.highlight .-Color[class*=-C119] {
  color: #87FF5F
}
div.highlight .-Color[class*=-BGC119] {
  background-color: #87FF5F
}
div.highlight .-Color[class*=-C120] {
  color: #87FF87
}
div.highlight .-Color[class*=-BGC120] {
  background-color: #87FF87
}
div.highlight .-Color[class*=-C121] {
  color: #87FFAF
}
div.highlight .-Color[class*=-BGC121] {
  background-color: #87FFAF
}
div.highlight .-Color[class*=-C122] {
  color: #87FFD7
}
div.highlight .-Color[class*=-BGC122] {
  background-color: #87FFD7
}
div.highlight .-Color[class*=-C123] {
  color: #87FFFF
}
div.highlight .-Color[class*=-BGC123] {
  background-color: #87FFFF
}
div.highlight .-Color[class*=-C124] {
  color: #AF0000
}
div.highlight .-Color[class*=-BGC124] {
  background-color: #AF0000
}
div.highlight .-Color[class*=-C125] {
  color: #AF005F
}
div.highlight .-Color[class*=-BGC125] {
  background-color: #AF005F
}
div.highlight .-Color[class*=-C126] {
  color: #AF0087
}
div.highlight .-Color[class*=-BGC126] {
  background-color: #AF0087
}
div.highlight .-Color[class*=-C127] {
  color: #AF00AF
}
div.highlight .-Color[class*=-BGC127] {
  background-color: #AF00AF
}
div.highlight .-Color[class*=-C128] {
  color: #AF00D7
}
div.highlight .-Color[class*=-BGC128] {
  background-color: #AF00D7
}
div.highlight .-Color[class*=-C129] {
  color: #AF00FF
}
div.highlight .-Color[class*=-BGC129] {
  background-color: #AF00FF
}
div.highlight .-Color[class*=-C130] {
  color: #AF5F00
}
div.highlight .-Color[class*=-BGC130] {
  background-color: #AF5F00
}
div.highlight .-Color[class*=-C131] {
  color: #AF5F5F
}
div.highlight .-Color[class*=-BGC131] {
  background-color: #AF5F5F
}
div.highlight .-Color[class*=-C132] {
  color: #AF5F87
}
div.highlight .-Color[class*=-BGC132] {
  background-color: #AF5F87
}
div.highlight .-Color[class*=-C133] {
  color: #AF5FAF
}
div.highlight .-Color[class*=-BGC133] {
  background-color: #AF5FAF
}
div.highlight .-Color[class*=-C134] {
  color: #AF5FD7
}
div.highlight .-Color[class*=-BGC134] {
  background-color: #AF5FD7
}
div.highlight .-Color[class*=-C135] {
  color: #AF5FFF
}
div.highlight .-Color[class*=-BGC135] {
  background-color: #AF5FFF
}
div.highlight .-Color[class*=-C136] {
  color: #AF8700
}
div.highlight .-Color[class*=-BGC136] {
  background-color: #AF8700
}
div.highlight .-Color[class*=-C137] {
  color: #AF875F
}
div.highlight .-Color[class*=-BGC137] {
  background-color: #AF875F
}
div.highlight .-Color[class*=-C138] {
  color: #AF8787
}
div.highlight .-Color[class*=-BGC138] {
  background-color: #AF8787
}
div.highlight .-Color[class*=-C139] {
  color: #AF87AF
}
div.highlight .-Color[class*=-BGC139] {
  background-color: #AF87AF
}
div.highlight .-Color[class*=-C140] {
  color: #AF87D7
}
div.highlight .-Color[class*=-BGC140] {
  background-color: #AF87D7
}
div.highlight .-Color[class*=-C141] {
  color: #AF87FF
}
div.highlight .-Color[class*=-BGC141] {
  background-color: #AF87FF
}
div.highlight .-Color[class*=-C142] {
  color: #AFAF00
}
div.highlight .-Color[class*=-BGC142] {
  background-color: #AFAF00
}
div.highlight .-Color[class*=-C143] {
  color: #AFAF5F
}
div.highlight .-Color[class*=-BGC143] {
  background-color: #AFAF5F
}
div.highlight .-Color[class*=-C144] {
  color: #AFAF87
}
div.highlight .-Color[class*=-BGC144] {
  background-color: #AFAF87
}
div.highlight .-Color[class*=-C145] {
  color: #AFAFAF
}
div.highlight .-Color[class*=-BGC145] {
  background-color: #AFAFAF
}
div.highlight .-Color[class*=-C146] {
  color: #AFAFD7
}
div.highlight .-Color[class*=-BGC146] {
  background-color: #AFAFD7
}
div.highlight .-Color[class*=-C147] {
  color: #AFAFFF
}
div.highlight .-Color[class*=-BGC147] {
  background-color: #AFAFFF
}
div.highlight .-Color[class*=-C148] {
  color: #AFD700
}
div.highlight .-Color[class*=-BGC148] {
  background-color: #AFD700
}
div.highlight .-Color[class*=-C149] {
  color: #AFD75F
}
div.highlight .-Color[class*=-BGC149] {
  background-color: #AFD75F
}
div.highlight .-Color[class*=-C150] {
  color: #AFD787
}
div.highlight .-Color[class*=-BGC150] {
  background-color: #AFD787
}
div.highlight .-Color[class*=-C151] {
  color: #AFD7AF
}
div.highlight .-Color[class*=-BGC151] {
  background-color: #AFD7AF
}
div.highlight .-Color[class*=-C152] {
  color: #AFD7D7
}
div.highlight .-Color[class*=-BGC152] {
  background-color: #AFD7D7
}
div.highlight .-Color[class*=-C153] {
  color: #AFD7FF
}
div.highlight .-Color[class*=-BGC153] {
  background-color: #AFD7FF
}
div.highlight .-Color[class*=-C154] {
  color: #AFFF00
}
div.highlight .-Color[class*=-BGC154] {
  background-color: #AFFF00
}
div.highlight .-Color[class*=-C155] {
  color: #AFFF5F
}
div.highlight .-Color[class*=-BGC155] {
  background-color: #AFFF5F
}
div.highlight .-Color[class*=-C156] {
  color: #AFFF87
}
div.highlight .-Color[class*=-BGC156] {
  background-color: #AFFF87
}
div.highlight .-Color[class*=-C157] {
  color: #AFFFAF
}
div.highlight .-Color[class*=-BGC157] {
  background-color: #AFFFAF
}
div.highlight .-Color[class*=-C158] {
  color: #AFFFD7
}
div.highlight .-Color[class*=-BGC158] {
  background-color: #AFFFD7
}
div.highlight .-Color[class*=-C159] {
  color: #AFFFFF
}
div.highlight .-Color[class*=-BGC159] {
  background-color: #AFFFFF
}
div.highlight .-Color[class*=-C160] {
  color: #D70000
}
div.highlight .-Color[class*=-BGC160] {
  background-color: #D70000
}
div.highlight .-Color[class*=-C161] {
  color: #D7005F
}
div.highlight .-Color[class*=-BGC161] {
  background-color: #D7005F
}
div.highlight .-Color[class*=-C162] {
  color: #D70087
}
div.highlight .-Color[class*=-BGC162] {
  background-color: #D70087
}
div.highlight .-Color[class*=-C163] {
  color: #D700AF
}
div.highlight .-Color[class*=-BGC163] {
  background-color: #D700AF
}
div.highlight .-Color[class*=-C164] {
  color: #D700D7
}
div.highlight .-Color[class*=-BGC164] {
  background-color: #D700D7
}
div.highlight .-Color[class*=-C165] {
  color: #D700FF
}
div.highlight .-Color[class*=-BGC165] {
  background-color: #D700FF
}
div.highlight .-Color[class*=-C166] {
  color: #D75F00
}
div.highlight .-Color[class*=-BGC166] {
  background-color: #D75F00
}
div.highlight .-Color[class*=-C167] {
  color: #D75F5F
}
div.highlight .-Color[class*=-BGC167] {
  background-color: #D75F5F
}
div.highlight .-Color[class*=-C168] {
  color: #D75F87
}
div.highlight .-Color[class*=-BGC168] {
  background-color: #D75F87
}
div.highlight .-Color[class*=-C169] {
  color: #D75FAF
}
div.highlight .-Color[class*=-BGC169] {
  background-color: #D75FAF
}
div.highlight .-Color[class*=-C170] {
  color: #D75FD7
}
div.highlight .-Color[class*=-BGC170] {
  background-color: #D75FD7
}
div.highlight .-Color[class*=-C171] {
  color: #D75FFF
}
div.highlight .-Color[class*=-BGC171] {
  background-color: #D75FFF
}
div.highlight .-Color[class*=-C172] {
  color: #D78700
}
div.highlight .-Color[class*=-BGC172] {
  background-color: #D78700
}
div.highlight .-Color[class*=-C173] {
  color: #D7875F
}
div.highlight .-Color[class*=-BGC173] {
  background-color: #D7875F
}
div.highlight .-Color[class*=-C174] {
  color: #D78787
}
div.highlight .-Color[class*=-BGC174] {
  background-color: #D78787
}
div.highlight .-Color[class*=-C175] {
  color: #D787AF
}
div.highlight .-Color[class*=-BGC175] {
  background-color: #D787AF
}
div.highlight .-Color[class*=-C176] {
  color: #D787D7
}
div.highlight .-Color[class*=-BGC176] {
  background-color: #D787D7
}
div.highlight .-Color[class*=-C177] {
  color: #D787FF
}
div.highlight .-Color[class*=-BGC177] {
  background-color: #D787FF
}
div.highlight .-Color[class*=-C178] {
  color: #D7AF00
}
div.highlight .-Color[class*=-BGC178] {
  background-color: #D7AF00
}
div.highlight .-Color[class*=-C179] {
  color: #D7AF5F
}
div.highlight .-Color[class*=-BGC179] {
  background-color: #D7AF5F
}
div.highlight .-Color[class*=-C180] {
  color: #D7AF87
}
div.highlight .-Color[class*=-BGC180] {
  background-color: #D7AF87
}
div.highlight .-Color[class*=-C181] {
  color: #D7AFAF
}
div.highlight .-Color[class*=-BGC181] {
  background-color: #D7AFAF
}
div.highlight .-Color[class*=-C182] {
  color: #D7AFD7
}
div.highlight .-Color[class*=-BGC182] {
  background-color: #D7AFD7
}
div.highlight .-Color[class*=-C183] {
  color: #D7AFFF
}
div.highlight .-Color[class*=-BGC183] {
  background-color: #D7AFFF
}
div.highlight .-Color[class*=-C184] {
  color: #D7D700
}
div.highlight .-Color[class*=-BGC184] {
  background-color: #D7D700
}
div.highlight .-Color[class*=-C185] {
  color: #D7D75F
}
div.highlight .-Color[class*=-BGC185] {
  background-color: #D7D75F
}
div.highlight .-Color[class*=-C186] {
  color: #D7D787
}
div.highlight .-Color[class*=-BGC186] {
  background-color: #D7D787
}
div.highlight .-Color[class*=-C187] {
  color: #D7D7AF
}
div.highlight .-Color[class*=-BGC187] {
  background-color: #D7D7AF
}
div.highlight .-Color[class*=-C188] {
  color: #D7D7D7
}
div.highlight .-Color[class*=-BGC188] {
  background-color: #D7D7D7
}
div.highlight .-Color[class*=-C189] {
  color: #D7D7FF
}
div.highlight .-Color[class*=-BGC189] {
  background-color: #D7D7FF
}
div.highlight .-Color[class*=-C190] {
  color: #D7FF00
}
div.highlight .-Color[class*=-BGC190] {
  background-color: #D7FF00
}
div.highlight .-Color[class*=-C191] {
  color: #D7FF5F
}
div.highlight .-Color[class*=-BGC191] {
  background-color: #D7FF5F
}
div.highlight .-Color[class*=-C192] {
  color: #D7FF87
}
div.highlight .-Color[class*=-BGC192] {
  background-color: #D7FF87
}
div.highlight .-Color[class*=-C193] {
  color: #D7FFAF
}
div.highlight .-Color[class*=-BGC193] {
  background-color: #D7FFAF
}
div.highlight .-Color[class*=-C194] {
  color: #D7FFD7
}
div.highlight .-Color[class*=-BGC194] {
  background-color: #D7FFD7
}
div.highlight .-Color[class*=-C195] {
  color: #D7FFFF
}
div.highlight .-Color[class*=-BGC195] {
  background-color: #D7FFFF
}
div.highlight .-Color[class*=-C196] {
  color: #FF0000
}
div.highlight .-Color[class*=-BGC196] {
  background-color: #FF0000
}
div.highlight .-Color[class*=-C197] {
  color: #FF005F
}
div.highlight .-Color[class*=-BGC197] {
  background-color: #FF005F
}
div.highlight .-Color[class*=-C198] {
  color: #FF0087
}
div.highlight .-Color[class*=-BGC198] {
  background-color: #FF0087
}
div.highlight .-Color[class*=-C199] {
  color: #FF00AF
}
div.highlight .-Color[class*=-BGC199] {
  background-color: #FF00AF
}
div.highlight .-Color[class*=-C200] {
  color: #FF00D7
}
div.highlight .-Color[class*=-BGC200] {
  background-color: #FF00D7
}
div.highlight .-Color[class*=-C201] {
  color: #FF00FF
}
div.highlight .-Color[class*=-BGC201] {
  background-color: #FF00FF
}
div.highlight .-Color[class*=-C202] {
  color: #FF5F00
}
div.highlight .-Color[class*=-BGC202] {
  background-color: #FF5F00
}
div.highlight .-Color[class*=-C203] {
  color: #FF5F5F
}
div.highlight .-Color[class*=-BGC203] {
  background-color: #FF5F5F
}
div.highlight .-Color[class*=-C204] {
  color: #FF5F87
}
div.highlight .-Color[class*=-BGC204] {
  background-color: #FF5F87
}
div.highlight .-Color[class*=-C205] {
  color: #FF5FAF
}
div.highlight .-Color[class*=-BGC205] {
  background-color: #FF5FAF
}
div.highlight .-Color[class*=-C206] {
  color: #FF5FD7
}
div.highlight .-Color[class*=-BGC206] {
  background-color: #FF5FD7
}
div.highlight .-Color[class*=-C207] {
  color: #FF5FFF
}
div.highlight .-Color[class*=-BGC207] {
  background-color: #FF5FFF
}
div.highlight .-Color[class*=-C208] {
  color: #FF8700
}
div.highlight .-Color[class*=-BGC208] {
  background-color: #FF8700
}
div.highlight .-Color[class*=-C209] {
  color: #FF875F
}
div.highlight .-Color[class*=-BGC209] {
  background-color: #FF875F
}
div.highlight .-Color[class*=-C210] {
  color: #FF8787
}
div.highlight .-Color[class*=-BGC210] {
  background-color: #FF8787
}
div.highlight .-Color[class*=-C211] {
  color: #FF87AF
}
div.highlight .-Color[class*=-BGC211] {
  background-color: #FF87AF
}
div.highlight .-Color[class*=-C212] {
  color: #FF87D7
}
div.highlight .-Color[class*=-BGC212] {
  background-color: #FF87D7
}
div.highlight .-Color[class*=-C213] {
  color: #FF87FF
}
div.highlight .-Color[class*=-BGC213] {
  background-color: #FF87FF
}
div.highlight .-Color[class*=-C214] {
  color: #FFAF00
}
div.highlight .-Color[class*=-BGC214] {
  background-color: #FFAF00
}
div.highlight .-Color[class*=-C215] {
  color: #FFAF5F
}
div.highlight .-Color[class*=-BGC215] {
  background-color: #FFAF5F
}
div.highlight .-Color[class*=-C216] {
  color: #FFAF87
}
div.highlight .-Color[class*=-BGC216] {
  background-color: #FFAF87
}
div.highlight .-Color[class*=-C217] {
  color: #FFAFAF
}
div.highlight .-Color[class*=-BGC217] {
  background-color: #FFAFAF
}
div.highlight .-Color[class*=-C218] {
  color: #FFAFD7
}
div.highlight .-Color[class*=-BGC218] {
  background-color: #FFAFD7
}
div.highlight .-Color[class*=-C219] {
  color: #FFAFFF
}
div.highlight .-Color[class*=-BGC219] {
  background-color: #FFAFFF
}
div.highlight .-Color[class*=-C220] {
  color: #FFD700
}
div.highlight .-Color[class*=-BGC220] {
  background-color: #FFD700
}
div.highlight .-Color[class*=-C221] {
  color: #FFD75F
}
div.highlight .-Color[class*=-BGC221] {
  background-color: #FFD75F
}
div.highlight .-Color[class*=-C222] {
  color: #FFD787
}
div.highlight .-Color[class*=-BGC222] {
  background-color: #FFD787
}
div.highlight .-Color[class*=-C223] {
  color: #FFD7AF
}
div.highlight .-Color[class*=-BGC223] {
  background-color: #FFD7AF
}
div.highlight .-Color[class*=-C224] {
  color: #FFD7D7
}
div.highlight .-Color[class*=-BGC224] {
  background-color: #FFD7D7
}
div.highlight .-Color[class*=-C225] {
  color: #FFD7FF
}
div.highlight .-Color[class*=-BGC225] {
  background-color: #FFD7FF
}
div.highlight .-Color[class*=-C226] {
  color: #FFFF00
}
div.highlight .-Color[class*=-BGC226] {
  background-color: #FFFF00
}
div.highlight .-Color[class*=-C227] {
  color: #FFFF5F
}
div.highlight .-Color[class*=-BGC227] {
  background-color: #FFFF5F
}
div.highlight .-Color[class*=-C228] {
  color: #FFFF87
}
div.highlight .-Color[class*=-BGC228] {
  background-color: #FFFF87
}
div.highlight .-Color[class*=-C229] {
  color: #FFFFAF
}
div.highlight .-Color[class*=-BGC229] {
  background-color: #FFFFAF
}
div.highlight .-Color[class*=-C230] {
  color: #FFFFD7
}
div.highlight .-Color[class*=-BGC230] {
  background-color: #FFFFD7
}
div.highlight .-Color[class*=-C231] {
  color: #FFFFFF
}
div.highlight .-Color[class*=-BGC231] {
  background-color: #FFFFFF
}
div.highlight .-Color[class*=-C232] {
  color: #080808
}
div.highlight .-Color[class*=-BGC232] {
  background-color: #080808
}
div.highlight .-Color[class*=-C233] {
  color: #121212
}
div.highlight .-Color[class*=-BGC233] {
  background-color: #121212
}
div.highlight .-Color[class*=-C234] {
  color: #1C1C1C
}
div.highlight .-Color[class*=-BGC234] {
  background-color: #1C1C1C
}
div.highlight .-Color[class*=-C235] {
  color: #262626
}
div.highlight .-Color[class*=-BGC235] {
  background-color: #262626
}
div.highlight .-Color[class*=-C236] {
  color: #303030
}
div.highlight .-Color[class*=-BGC236] {
  background-color: #303030
}
div.highlight .-Color[class*=-C237] {
  color: #3A3A3A
}
div.highlight .-Color[class*=-BGC237] {
  background-color: #3A3A3A
}
div.highlight .-Color[class*=-C238] {
  color: #444444
}
div.highlight .-Color[class*=-BGC238] {
  background-color: #444444
}
div.highlight .-Color[class*=-C239] {
  color: #4E4E4E
}
div.highlight .-Color[class*=-BGC239] {
  background-color: #4E4E4E
}
div.highlight .-Color[class*=-C240] {
  color: #585858
}
div.highlight .-Color[class*=-BGC240] {
  background-color: #585858
}
div.highlight .-Color[class*=-C241] {
  color: #626262
}
div.highlight .-Color[class*=-BGC241] {
  background-color: #626262
}
div.highlight .-Color[class*=-C242] {
  color: #6C6C6C
}
div.highlight .-Color[class*=-BGC242] {
  background-color: #6C6C6C
}
div.highlight .-Color[class*=-C243] {
  color: #767676
}
div.highlight .-Color[class*=-BGC243] {
  background-color: #767676
}
div.highlight .-Color[class*=-C244] {
  color: #808080
}
div.highlight .-Color[class*=-BGC244] {
  background-color: #808080
}
div.highlight .-Color[class*=-C245] {
  color: #8A8A8A
}
div.highlight .-Color[class*=-BGC245] {
  background-color: #8A8A8A
}
div.highlight .-Color[class*=-C246] {
  color: #949494
}
div.highlight .-Color[class*=-BGC246] {
  background-color: #949494
}
div.highlight .-Color[class*=-C247] {
  color: #9E9E9E
}
div.highlight .-Color[class*=-BGC247] {
  background-color: #9E9E9E
}
div.highlight .-Color[class*=-C248] {
  color: #A8A8A8
}
div.highlight .-Color[class*=-BGC248] {
  background-color: #A8A8A8
}
div.highlight .-Color[class*=-C249] {
  color: #B2B2B2
}
div.highlight .-Color[class*=-BGC249] {
  background-color: #B2B2B2
}
div.highlight .-Color[class*=-C250] {
  color: #BCBCBC
}
div.highlight .-Color[class*=-BGC250] {
  background-color: #BCBCBC
}
div.highlight .-Color[class*=-C251] {
  color: #C6C6C6
}
div.highlight .-Color[class*=-BGC251] {
  background-color: #C6C6C6
}
div.highlight .-Color[class*=-C252] {
  color: #D0D0D0
}
div.highlight .-Color[class*=-BGC252] {
  background-color: #D0D0D0
}
div.highlight .-Color[class*=-C253] {
  color: #DADADA
}
div.highlight .-Color[class*=-BGC253] {
  background-color: #DADADA
}
div.highlight .-Color[class*=-C254] {
  color: #E4E4E4
}
div.highlight .-Color[class*=-BGC254] {
  background-color: #E4E4E4
}
div.highlight .-Color[class*=-C255] {
  color: #EEEEEE
}
div.highlight .-Color[class*=-BGC255] {
  background-color: #EEEEEE
}
