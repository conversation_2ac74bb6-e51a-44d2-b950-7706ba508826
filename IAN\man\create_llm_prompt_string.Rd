% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/IAN_llm_prompts.R
\name{create_llm_prompt_string}
\alias{create_llm_prompt_string}
\title{Create LLM Prompt for STRING Interaction Analysis}
\usage{
create_llm_prompt_string(
  interaction_results,
  analysis_type,
  pathway_results = NULL,
  chea_results = NULL,
  gene_symbols = NULL,
  string_network_properties = NULL,
  experimental_design = NULL
)
}
\arguments{
\item{interaction_results}{A list containing STRING interaction results. Must have elements `interactions` (data frame of interactions) and `network_properties` (data frame of network properties).}

\item{analysis_type}{Character string specifying the type of analysis (e.g., "STRING").}

\item{pathway_results}{A list containing pathway enrichment results (KEGG, WikiPathways, Reactome, GO) (optional).}

\item{chea_results}{A data frame containing ChEA transcription factor enrichment results (optional).}

\item{gene_symbols}{A vector of gene symbols used in the analysis.}

\item{string_network_properties}{A data frame containing STRING network properties (optional).}

\item{experimental_design}{A character string describing the experimental design (optional).}
}
\value{
A character string containing the LLM prompt.
}
\description{
Creates a prompt for a Large Language Model (LLM) to analyze STRING protein-protein interaction data,
along with other relevant data such as pathway enrichment results and ChEA transcription factor enrichment.
}
