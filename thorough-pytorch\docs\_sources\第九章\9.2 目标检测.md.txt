# 目标检测
## 目标检测概述
目标检测是计算机视觉的一个重要任务，根据整张图像内容进行描述，并结合目标物体的特征信息，确定该物体的类别与位置。不同于图像分类任务中我们只需要输出图像中主要物体对象的类标，在目标检测任务中，一张图像里往往含有多个物体对象，我们不仅需要输出这些物体的类标，同时还需要输出这些物体的位置，在表示位置时，我们一般采用目标检测边缘框bounding box进行表示，bounding box是一组坐标值，常见形式为（x1,y1,x2,y2），其中x1代表物体左上x坐标，y1代表左上y坐标，x2代表物体右下x坐标，y2代表物体右下y坐标。
## 目标检测应用
目标检测技术不同于图像分类单一输出物体的种类，它将物体的位置和种类一起输出，这使得目标检测在一些领域有着重要的作用，目标检测常用于人脸检测、智慧交通、机器人、无人驾驶、遥感目标检测、安防领域检测异常、行人计数、安全系统等各大领域。
## 目标检测数据集
目标检测的数据集通常来说比图片分类的数据集小很多，因为每一张图片的标注的成本很高，相较于图片分类的常见标注方法是给定一个CSV文件（图片与标号一一对应）或者是给定一个文件夹（每个类对应一个子文件夹，对应标号的图片放在子文件夹下），但是对于目标检测来说因为一张图片中可能存在多个类，所以我们就不能放在子文件夹中，所以通常来说目标检测的数据集的标号需要额外存储，常见的存储格式有PASCAL VOC的格式和COCO的标注格式。假设使用文本文件存储的话，每一行表示一个物体，每一行分别由图片文件名（因为一张图片中可能有多个物体，所以同一个文件名可能会出现多次）、物体类别（标号）、边缘框（图片中物体的位置）组成，每一行一共有6（1+1+4）个值 
目标检测常用的数据集有PASCAL VOC2007, MS COCO
### COCO数据集
目标检测中比较常见的数据集，类似于Imagenet在图片分类中的地位
访问地址：https://cocodataset.org/#home
COCO数据集中有 80 个类别，330k 图片，1.5M 物体（每张图片中有多个物体） 

## 目标检测常用算法
随着算力的发展和深度学习的发展，目标检测经历了从基于手工设计特征的方法到基于深度学习提取特征的阶段。在早期，目标检测技术往往采用手工设计特征（Haar特征、梯度直方图HOG）加分类器（SVM、AdaBoost）的方法实现。随着卷积神经网络的发展，目标检测出现了一系列的基于卷积神经网络的目标检测技术，包括R-CNN系列,SSD系列,YOLO系列等。随着Transformer在自然语言处理和计算机视觉的发展，也出现了基于Transformer的目标检测技术，代表工作有DETR系列。在本部分，我们主要介绍基于深度学习的目标检测技术并进行部分代码的解读。
我们可以将基于深度学习的目标检测技术按照有无使用锚点框分为基于锚点框的目标检测方法（Anchor-based），无锚点框的目标检测方法（Anchor-free）和端到端的目标检测方法（Anchor-free）。其中端到端的目标检测技术也是属于特殊的无锚点框的目标检测方法。
我们可以将基于锚点框的目标检测方法分为单阶段目标检测方法（One-Stage）和两阶段目标检测方法（Two-Stage），其中单阶段目标检测方法代表作有YOLO (You Only Look Once)和SSD (Single Shot Detector)，两阶段目标检测方法的代表作有R-CNN（R-CNN，Fast RCNN,Faster RCNN，Mask RCNN，Cascade RCNN，SPPNet）系列。一般而言，两阶段目标检测具有较高的检测精度，而单阶段目标检测方法具有更高的精度。
同样，我们可以将无锚点框的目标检测方法分为基于目标点的目标检测方法和基于内部点的目标检测方法。
端到端的目标检测方法




