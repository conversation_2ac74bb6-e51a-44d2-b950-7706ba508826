# 前言

2022年底，ChatGPT 的横空出世改变了人们对人工智能的认知，也给自然语言处理（Natural Language Process，NLP）领域带来了阶段性的变革，以 GPT 系列模型为代表的大语言模型（Large Language Model，LLM）成为 NLP 乃至人工智能领域的研究主流。自 2023年至今，LLM 始终是人工智能领域的核心话题，引发了一轮又一轮的科技浪潮。

LLM 其实是 NLP 领域经典研究方法预训练语言模型（Pretrain Language Model，PLM）的一种衍生成果。NLP 领域聚焦于人类书写的自然语言文本的处理、理解和生成，从诞生至今经历了符号主义阶段、统计学习阶段、深度学习阶段、预训练模型阶段到而今大模型阶段的多次变革。以 GPT、BERT 为代表的 PLM 是上一阶段 NLP 领域的核心研究成果，以注意力机制为模型架构，通过预训练-微调的阶段思想通过在海量无监督文本上进行自监督预训练，实现了强大的自然语言理解能力。但是，传统的 PLM 仍然依赖于一定量有监督数据进行下游任务微调，且在自然语言生成任务上性能还不尽如人意，NLP 系统的性能距离人们所期待的通用人工智能还有不小的差距。

LLM 是在 PLM 的基础上，通过大量扩大模型参数、预训练数据规模，并引入指令微调、人类反馈强化学习等手段实现的突破性成果。相较于传统 PLM，LLM 具备涌现能力，具有强大的上下文学习能力、指令理解能力和文本生成能力。在大模型阶段，NLP 研究者可以一定程度抛弃大量的监督数据标注工作，通过提供少量监督示例，LLM 即能在指定下游任务上达到媲美大规模微调 PLM 的性能。同时，强大的指令理解能力与文本生成能力使 LLM 能够直接、高效、准确地响应用户指令，从而真正向通用人工智能的目标逼近。

LLM 的突破性进展激发了 NLP 领域乃至整个人工智能领域的研究热度，海内外高校、研究院、大厂乃至众多传统领域企业都投入到 LLM 研究的浪潮中。自 2023年至今，LLM 阶段性成果层出不穷，模型性能不断刷新上限，从一开始的 ChatGPT，到 GPT-4，再到以 DeepSeek-R1 为代表的推理大模型、以 Qwen-VL 为代表的多模态大模型等更强大、更定制化的模型，LLM 应用也不断涌现出能够提升实际生产力、赋能用户实际生活的创新应用，从”百模大战“到”Agent 元年“，LLM 基座研究或许已趋向稳定的格局，LLM 的研究始终方兴未艾。可以肯定的是，在并不遥远的未来，LLM 及以 LLM 为基础的应用一定会成为人们生活中的基础设施，与每个人的生活、学习、工作密不可分。

在这样的背景下，深入理解、掌握 LLM 原理，能够动手应用、训练任意一个 LLM 的能力，对每一位 NLP 研究者乃至其他领域的 AI 研究者至关重要。我们在 2023年底分别创建了 self-llm（开源大模型食用指南：https://github.com/datawhalechina/self-llm ）、llm-universe（动手学大模型应用开发：https://github.com/datawhalechina/llm-universe ）两个原创开源大模型教程，前者旨在为开发者提供一站式开源 LLM 部署、推理、微调的使用教程，后者旨在指导开发者从零开始搭建自己的 LLM 应用。两个教程都帮助到了广泛的国内外开发者，也获得了众多开发者的支持和认可，在学习者的反馈中，我们发现目前还缺乏一个从零开始讲解 LLM 原理、并引导学习者亲手搭建、训练 LLM 的完整教程。

鉴于此，我们编写了这本结合 LLM 原理及实战的教程。本书将从 NLP 的基本研究方法出发，根据 LLM 的思路及原理逐层深入，依次为读者剖析 LLM 的架构基础和训练过程。同时，我们会结合目前 LLM 领域最主流的代码框架，演练如何亲手搭建、训练一个 LLM，期以实现授之以鱼，更授之以渔。希望读者能从这本书开始走入 LLM 的浩瀚世界，探索 LLM 的无尽可能。

## 写给读者的建议

本书包含 LLM 的理论基础、原理介绍和项目实战，全书包括 LLM 及 NLP 的核心思路剖析、公式解析与代码实战，旨在帮助开发者深入理解并掌握 LLM 的基本原理与应用。因此，本书适合大学生、研究人员、LLM 爱好者阅读。在阅读本书之前，你需要具备一定的编程经验，尤其是要对 Python 编程语言有一定的了解。同时，你最好具备深度学习的相关知识，并了解 NLP 领域的相关概念和术语，以便更轻松地阅读本书。

本书分为两部分——基础知识与实战应用。第1章～第4章是基础知识部分，从浅入深介绍 LLM 的基本原理。其中，第1章简单介绍 NLP 的基本任务和发展，为非 NLP 领域研究者提供参考；第2章介绍 LLM 的基本架构——Transformer，包括原理介绍及代码实现，作为 LLM 最重要的理论基础；第3章整体介绍经典的 PLM，包括 Encoder-Only、Encoder-Decoder 和 Decoder-Only 三种架构，也同时介绍了当前一些主流 LLM 的架构和思想；第4章则正式进入 LLM 部分，详细介绍 LLM 的特点、能力和整体训练过程。第5章～第7章是实战应用部分，将逐步带领读者深入 LLM 的底层细节。其中，第5章将带领读者基于 PyTorch 层亲手搭建一个 LLM，并实现预训练、有监督微调的全流程；第6章将引入目前业界主流的 LLM 训练框架 Transformers，带领读者基于该框架快速、高效地实现 LLM 训练过程；第7章则将介绍 基于 LLM 的各种应用，补全读者对 LLM 体系的认知，包括 LLM 的评测、检索增强生成（Retrieval-Augmented Generation，RAG）、智能体（Agent）的思想和简单实现。你可以根据个人兴趣和需求，选择性地阅读相关章节。

在阅读本书的过程中，建议你将理论和实际相结合。LLM 是一个快速发展、注重实践的领域，我们建议你多投入实战，复现本书提供的各种代码，同时积极参加 LLM 相关的项目与比赛，真正投入到 LLM 开发的浪潮中。我们鼓励你关注 Datawhale 及其他 LLM 相关开源社区，当遇到问题时，你可以随时在 Datawhale 社区提问。Datawhale 也会始终跟进 LLM 及其他人工智能技术的发展，欢迎你关注或加入到 Datawhale 社区的共建中。

最后，欢迎每一位读者在阅读完本书后加入到 LLM 开发者的行列。作为国内 AI 开源社区，我们希望充分聚集共创者，一起丰富这个开源 LLM 的世界，打造更多、更全面特色 LLM 的教程。星火点点，汇聚成海。我们希望成为 LLM 与普罗大众的阶梯，以自由、平等的开源精神，拥抱更恢弘而辽阔的 LLM 世界。

感谢你选择本书，祝你阅读愉快！
