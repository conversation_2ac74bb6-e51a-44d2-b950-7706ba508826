{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8c3d4501-a268-418a-b5f6-59078094aab5", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "fw = open(\"autodl-tmp/dataset/pretrain_data/mobvoi_seq_monkey_general_open_corpus_small.jsonl\", \"w\")\n", "i = 0\n", "with open(\"autodl-tmp/dataset/pretrain_data/mobvoi_seq_monkey_general_open_corpus.jsonl\", \"r\") as f:\n", "    while i <= 1000000:\n", "        line = f.readline()\n", "        fw.write(line)\n", "        i += 1\n", "fw.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}